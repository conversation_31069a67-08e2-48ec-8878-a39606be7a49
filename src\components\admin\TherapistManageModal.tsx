
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Title } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "react-i18next";

interface Therapist {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  patient_count?: number;
}

interface TherapistManageModalProps {
  isOpen: boolean;
  onClose: () => void;
  therapist: Therapist | null;
  onUpdate: () => void;
}

export function TherapistManageModal({ isOpen, onClose, therapist, onUpdate }: TherapistManageModalProps) {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    first_name: "",
    last_name: "",
    email: "",
  });
  const { toast } = useToast();
  const { t } = useTranslation();

  useEffect(() => {
    if (therapist) {
      setFormData({
        first_name: therapist.first_name || "",
        last_name: therapist.last_name || "",
        email: therapist.email || "",
      });
    }
  }, [therapist]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!therapist) return;

    setLoading(true);
    try {
      const { error } = await supabase
        .from("profiles")
        .update(formData)
        .eq("id", therapist.id);

      if (error) throw error;

      toast({
        title: t("common.success"),
        description: t("admin.therapistUpdatedSuccessfully"),
      });
      onUpdate();
      onClose();
    } catch (error: any) {
      toast({
        title: t("common.error"),
        description: t("admin.failedToUpdateTherapist"),
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{t("admin.manageTherapist")}</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="first-name">{t("auth.firstName")}</Label>
            <Input
              id="first-name"
              value={formData.first_name}
              onChange={(e) => setFormData({ ...formData, first_name: e.target.value })}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="last-name">{t("auth.lastName")}</Label>
            <Input
              id="last-name"
              value={formData.last_name}
              onChange={(e) => setFormData({ ...formData, last_name: e.target.value })}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="email">{t("auth.email")}</Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              required
            />
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              {t("common.cancel")}
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? t("admin.updating") : t("admin.updateTherapist")}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
