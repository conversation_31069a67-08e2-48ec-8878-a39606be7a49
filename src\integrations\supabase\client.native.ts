// React Native compatible Supabase client
import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';
import type { Database } from './types';

const SUPABASE_URL = "https://jttsprfcdcoorslznlcc.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imp0dHNwcmZjZGNvb3JzbHpubGNjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkxMTUyMTAsImV4cCI6MjA2NDY5MTIxMH0.frC0HgjhbCTzElSOUoApyHXyP-0hXAFvVhkp26AjeJc";

// Create Supabase client with AsyncStorage for React Native
export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});
