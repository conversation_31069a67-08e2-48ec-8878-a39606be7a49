import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Activity, Plus, Search, ArrowLeft } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { Layout } from "@/components/Layout";
import { Skeleton } from "@/components/ui/skeleton";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useTranslation } from "react-i18next";

interface DailyActivity {
  id: string;
  title: string;
  description: string;
  category: string;
  scheduled_time: string;
  is_active: boolean;
  patient: {
    guardian_name: string;
    user: {
      first_name: string;
      last_name: string;
    } | null;
  } | null;
}

interface Patient {
  id: string;
  guardian_name: string;
  user: {
    first_name: string;
    last_name: string;
  } | null;
}

export default function ActivitiesManagement() {
  const [activities, setActivities] = useState<DailyActivity[]>([]);
  const [patients, setPatients] = useState<Patient[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newActivity, setNewActivity] = useState({
    patient_id: "",
    title: "",
    description: "",
    category: "social",
    scheduled_time: "",
    start_date: "",
    end_date: ""
  });
  const { profile } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { t } = useTranslation();

  useEffect(() => {
    if (profile?.id) {
      fetchActivities();
      fetchPatients();
    }
  }, [profile]);

  useEffect(() => {
    // Set patient from URL parameter and show create form
    const patientId = searchParams.get('patient');
    if (patientId && patients.length > 0) {
      console.log("Setting patient from URL:", patientId);
      setNewActivity(prev => ({ ...prev, patient_id: patientId }));
      setShowCreateForm(true);
    }
  }, [searchParams, patients]);

  const fetchActivities = async () => {
    if (!profile?.id) {
      console.log("No profile ID available for activities");
      setLoading(false);
      return;
    }

    try {
      console.log("Fetching activities for therapist:", profile.id);
      const { data, error } = await supabase
        .from("daily_activities")
        .select(`
          *,
          patient:patients(
            guardian_name,
            user:profiles!patients_user_id_fkey(first_name, last_name)
          )
        `)
        .eq("therapist_id", profile.id)
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching activities:", error);
        throw error;
      }
      
      console.log("Activities fetched successfully:", data);
      setActivities((data as any) || []);
    } catch (error: any) {
      console.error("Activities fetch error:", error);
      toast({
        title: "Error",
        description: "Failed to fetch activities",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchPatients = async () => {
    if (!profile?.id) {
      console.log("No profile ID available for patients");
      return;
    }

    try {
      console.log("Fetching patients for therapist:", profile.id);
      const { data, error } = await supabase
        .from("patients")
        .select(`
          id,
          guardian_name,
          user:profiles!patients_user_id_fkey(first_name, last_name)
        `)
        .eq("assigned_therapist_id", profile.id);

      if (error) {
        console.error("Error fetching patients:", error);
        throw error;
      }
      
      console.log("Patients fetched successfully:", data);
      setPatients((data as any) || []);
    } catch (error: any) {
      console.error("Patients fetch error:", error);
      toast({
        title: "Error",
        description: "Failed to fetch patients",
        variant: "destructive",
      });
    }
  };

  const createActivity = async () => {
    if (!profile?.id) {
      toast({
        title: "Error",
        description: "User profile not loaded",
        variant: "destructive",
      });
      return;
    }

    if (!newActivity.patient_id || !newActivity.title) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    try {
      const { error } = await supabase
        .from("daily_activities")
        .insert({
          patient_id: newActivity.patient_id,
          therapist_id: profile.id,
          title: newActivity.title,
          description: newActivity.description,
          category: newActivity.category,
          scheduled_time: newActivity.scheduled_time || null,
          start_date: newActivity.start_date || null,
          end_date: newActivity.end_date || null,
          is_active: true
        });

      if (error) throw error;

      toast({
        title: "Success",
        description: "Activity created successfully",
      });

      setShowCreateForm(false);
      setNewActivity({
        patient_id: "",
        title: "",
        description: "",
        category: "social",
        scheduled_time: "",
        start_date: "",
        end_date: ""
      });
      fetchActivities();
    } catch (error: any) {
      toast({
        title: "Error",
        description: "Failed to create activity",
        variant: "destructive",
      });
    }
  };

  const filteredActivities = activities.filter(activity =>
    activity.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    activity.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    activity.patient?.user?.first_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    activity.patient?.user?.last_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    activity.patient?.guardian_name?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getSelectedPatientName = () => {
    if (!newActivity.patient_id) return t("therapist.selectPatient");
    const selectedPatient = patients.find(p => p.id === newActivity.patient_id);
    if (!selectedPatient) return t("therapist.selectPatient");
    
    // If user profile exists, use it; otherwise use guardian_name
    if (selectedPatient.user?.first_name && selectedPatient.user?.last_name) {
      return `${selectedPatient.user.first_name} ${selectedPatient.user.last_name}`;
    }
    return selectedPatient.guardian_name || t("therapist.unknownPatient");
  };

  const getPatientDisplayName = (patient: Patient) => {
    // If user profile exists, use it; otherwise use guardian_name
    if (patient.user?.first_name && patient.user?.last_name) {
      return `${patient.user.first_name} ${patient.user.last_name}`;
    }
    return patient.guardian_name || t("therapist.unknownPatient");
  };

  const getActivityPatientName = (activity: DailyActivity) => {
    if (!activity.patient) return t("therapist.unknownPatient");
    
    // If user profile exists, use it; otherwise use guardian_name
    if (activity.patient.user?.first_name && activity.patient.user?.last_name) {
      return `${activity.patient.user.first_name} ${activity.patient.user.last_name}`;
    }
    return activity.patient.guardian_name || t("therapist.unknownPatient");
  };

  if (loading) {
    return (
      <Layout>
        <div className="min-h-screen bg-gray-50 p-6">
          <div className="max-w-4xl mx-auto space-y-6">
            <Skeleton className="h-12 w-64" />
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <Skeleton key={i} className="h-32 rounded-2xl" />
              ))}
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white px-6 py-6 flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate("/dashboard")}
            className="p-2"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-2xl font-bold text-gray-900">{t("therapist.activitiesManagement")}</h1>
        </div>

        <div className="max-w-4xl mx-auto px-6 space-y-6">
          {/* Search and Actions */}
          <div className="flex items-center justify-between gap-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder={t("therapist.searchActivities")}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 h-12 rounded-xl border-gray-200"
              />
            </div>
            <Button 
              onClick={() => setShowCreateForm(!showCreateForm)}
              className="bg-orange-500 hover:bg-orange-600 text-white rounded-xl h-12 px-6"
            >
              <Plus className="h-4 w-4 mr-2" />
              {t("therapist.createActivity")}
            </Button>
          </div>

          {/* Create Activity Form */}
          {showCreateForm && (
            <Card className="bg-white border-0 shadow-lg rounded-2xl">
              <CardHeader>
                <CardTitle className="text-orange-600">{t("therapist.createNewActivity")}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Select value={newActivity.patient_id} onValueChange={(value) => setNewActivity({...newActivity, patient_id: value})}>
                  <SelectTrigger className="rounded-xl">
                    <SelectValue>
                      {getSelectedPatientName()}
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    {patients.map((patient) => (
                      <SelectItem key={patient.id} value={patient.id}>
                        {getPatientDisplayName(patient)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Input 
                  placeholder={t("therapist.activityTitle")} 
                  value={newActivity.title}
                  onChange={(e) => setNewActivity({...newActivity, title: e.target.value})}
                  className="rounded-xl" 
                />
                
                <Textarea 
                  placeholder={t("therapist.activityDescription")} 
                  value={newActivity.description}
                  onChange={(e) => setNewActivity({...newActivity, description: e.target.value})}
                  className="rounded-xl" 
                />

                <Select value={newActivity.category} onValueChange={(value) => setNewActivity({...newActivity, category: value})}>
                  <SelectTrigger className="rounded-xl">
                    <SelectValue placeholder={t("therapist.selectCategory")} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="social">{t("categories.social")}</SelectItem>
                    <SelectItem value="communication">{t("categories.communication")}</SelectItem>
                    <SelectItem value="self-care">{t("therapist.selfCare")}</SelectItem>
                    <SelectItem value="academic">{t("therapist.academic")}</SelectItem>
                    <SelectItem value="behavioral">{t("categories.behavior")}</SelectItem>
                  </SelectContent>
                </Select>

                <Input 
                  type="time" 
                  placeholder={t("therapist.scheduledTime")}
                  value={newActivity.scheduled_time}
                  onChange={(e) => setNewActivity({...newActivity, scheduled_time: e.target.value})}
                  className="rounded-xl" 
                />

                <div className="grid grid-cols-2 gap-4">
                  <Input 
                    type="date" 
                    placeholder={t("therapist.startDate")}
                    value={newActivity.start_date}
                    onChange={(e) => setNewActivity({...newActivity, start_date: e.target.value})}
                    className="rounded-xl" 
                  />
                  <Input 
                    type="date" 
                    placeholder={t("therapist.endDate")}
                    value={newActivity.end_date}
                    onChange={(e) => setNewActivity({...newActivity, end_date: e.target.value})}
                    className="rounded-xl" 
                  />
                </div>

                <div className="flex space-x-2">
                  <Button 
                    variant="outline" 
                    onClick={() => setShowCreateForm(false)}
                    className="rounded-xl"
                  >
                    {t("common.cancel")}
                  </Button>
                  <Button 
                    onClick={createActivity}
                    className="bg-orange-500 hover:bg-orange-600 text-white rounded-xl"
                    disabled={!newActivity.patient_id || !newActivity.title}
                  >
                    {t("therapist.createActivity")}
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Activities List */}
          <div className="space-y-4">
            {activities.length === 0 ? (
              <Card className="bg-white border-0 shadow-lg rounded-2xl">
                <CardContent className="p-12 text-center">
                  <Activity className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">{t("therapist.noActivitiesCreated")}</h3>
                  <p className="text-gray-600">{t("therapist.startByCreatingActivities")}</p>
                </CardContent>
              </Card>
            ) : (
              filteredActivities.map((activity) => (
                <Card key={activity.id} className="bg-white border-0 shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300">
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div className="space-y-3">
                        <CardTitle className="text-lg text-gray-900">{activity.title}</CardTitle>
                        <div className="flex items-center space-x-3">
                          <Badge className="bg-orange-500 text-white">
                            {t(`categories.${activity.category}`) || activity.category}
                          </Badge>
                          <Badge 
                            className={`${
                              activity.is_active ? 'bg-green-500 text-white' : 'bg-gray-500 text-white'
                            }`}
                          >
                            {activity.is_active ? t("status.active") : t("therapist.inactive")}
                          </Badge>
                        </div>
                      </div>
                      <div className="text-right">
                        <span className="text-sm text-gray-600">{t("patient.scheduled")}</span>
                        <div className="text-sm font-semibold text-gray-900">
                          {activity.scheduled_time ? 
                            new Date(`2000-01-01T${activity.scheduled_time}`).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) : 
                            t("therapist.anytime")
                          }
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-gray-600">{activity.description}</p>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-600 mb-1">{t("therapist.patient")}:</p>
                        <p className="font-semibold text-gray-900">
                          {getActivityPatientName(activity)}
                        </p>
                      </div>
                      <div className="flex space-x-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          className="rounded-xl border-orange-500 text-orange-600 hover:bg-orange-500 hover:text-white"
                        >
                          {t("common.edit")}
                        </Button>
                        <Button 
                          size="sm"
                          className="bg-orange-500 hover:bg-orange-600 text-white rounded-xl"
                        >
                          {t("admin.manage")}
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>

          {/* Bottom spacing */}
          <div className="h-8"></div>
        </div>
      </div>
    </Layout>
  );
}
