
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { patients, type } = await req.json();
    const geminiApiKey = Deno.env.get('GEMINI_API_KEY');
    
    if (!geminiApiKey) {
      throw new Error('GEMINI_API_KEY not configured');
    }

    let prompt = `As an AI autism therapy assistant, analyze this patient data and provide professional insights for therapists:

Patient Overview:
- Total Patients: ${patients.length}
- Average Completion Rate: ${patients.length > 0 ? Math.round(patients.reduce((sum: number, p: any) => sum + (p.completion_rate || 0), 0) / patients.length) : 0}%
- Total Active Goals: ${patients.reduce((sum: number, p: any) => sum + (p.goals_count || 0), 0)}

`;

    if (type === 'patient_overview') {
      prompt += `Provide professional insights (2-3 sentences) about patient engagement patterns and recommendations for improving therapy outcomes.`;
    } else if (type === 'goal_recommendations') {
      prompt += `Provide evidence-based recommendations (2-3 sentences) for setting effective therapy goals based on the current patient data.`;
    } else {
      prompt += `Provide general professional insights (2-3 sentences) to help improve therapy practice and patient outcomes.`;
    }

    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${geminiApiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: prompt
          }]
        }]
      }),
    });

    const data = await response.json();
    const insight = data.candidates?.[0]?.content?.parts?.[0]?.text || "Continue monitoring patient progress and adjust therapy plans based on individual needs and response patterns.";

    return new Response(JSON.stringify({ insight }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Error:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
