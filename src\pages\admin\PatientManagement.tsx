
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Search, Plus, User, Calendar, ArrowLeft } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { Layout } from "@/components/Layout";
import { Skeleton } from "@/components/ui/skeleton";
import { useNavigate } from "react-router-dom";
import { PatientManageModal } from "@/components/admin/PatientManageModal";
import { useTranslation } from "react-i18next";

interface Patient {
  id: string;
  guardian_name: string;
  date_of_birth: string;
  autism_level: string;
  assigned_therapist_id: string;
  created_at: string;
  medical_notes?: string;
  emergency_contact?: string;
  therapist?: {
    first_name: string;
    last_name: string;
  };
  user?: {
    first_name: string;
    last_name: string;
  };
}

interface Therapist {
  id: string;
  first_name: string;
  last_name: string;
}

export default function PatientManagement() {
  const [patients, setPatients] = useState<Patient[]>([]);
  const [therapists, setTherapists] = useState<Therapist[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [showManageModal, setShowManageModal] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();
  const { t } = useTranslation();

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      // Fetch patients
      const { data: patientsData, error: patientsError } = await supabase
        .from("patients")
        .select(`
          *,
          therapist:profiles!assigned_therapist_id(first_name, last_name),
          user:profiles!user_id(first_name, last_name)
        `)
        .order("created_at", { ascending: false });

      if (patientsError) throw patientsError;

      // Fetch all therapists
      const { data: therapistsData, error: therapistsError } = await supabase
        .from("profiles")
        .select("id, first_name, last_name")
        .eq("role", "therapist")
        .order("first_name");

      if (therapistsError) throw therapistsError;

      setPatients(patientsData || []);
      setTherapists(therapistsData || []);
    } catch (error: any) {
      toast({
        title: t("common.error"),
        description: t("admin.failedToFetchData"),
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleManage = (patient: Patient) => {
    setSelectedPatient(patient);
    setShowManageModal(true);
  };

  const handleViewProfile = (patient: Patient) => {
    toast({
      title: t("admin.patientProfile"),
      description: `${patient.user?.first_name} ${patient.user?.last_name} - ${t("autismLevels." + patient.autism_level)} ${t("patient.autismLevel")}`,
    });
  };

  const filteredPatients = patients.filter(patient =>
    patient.guardian_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.user?.first_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    patient.user?.last_name?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <Layout>
        <div className="min-h-screen bg-gray-50 p-6">
          <div className="max-w-6xl mx-auto space-y-6">
            <Skeleton className="h-12 w-64" />
            <div className="grid gap-6">
              {[1, 2, 3, 4].map((i) => (
                <Skeleton key={i} className="h-32 rounded-2xl" />
              ))}
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white px-6 py-6 flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate("/dashboard")}
            className="p-2"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-2xl font-bold text-gray-900">{t("admin.patientManagement")}</h1>
        </div>

        <div className="max-w-6xl mx-auto px-6 space-y-6">
          {/* Search and Actions */}
          <div className="flex items-center justify-between gap-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder={t("admin.searchPatients")}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 h-12 rounded-xl border-gray-200"
              />
            </div>
            <Button className="bg-app-purple hover:bg-app-purple/90 text-white rounded-xl h-12 px-6">
              <Plus className="h-4 w-4 mr-2" />
              {t("admin.addPatient")}
            </Button>
          </div>

          {/* Patients Grid */}
          <div className="grid gap-6">
            {filteredPatients.length === 0 ? (
              <Card className="bg-white border-0 shadow-lg rounded-2xl">
                <CardContent className="p-12 text-center">
                  <User className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">{t("admin.noPatientsFound")}</h3>
                  <p className="text-gray-600">
                    {searchTerm ? t("admin.noPatientsMatch") : t("admin.noPatientsRegistered")}
                  </p>
                </CardContent>
              </Card>
            ) : (
              filteredPatients.map((patient) => (
                <Card key={patient.id} className="bg-white border-0 shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300">
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div className="space-y-3">
                        <CardTitle className="text-lg text-gray-900">
                          {patient.user?.first_name} {patient.user?.last_name} 
                          {patient.guardian_name && patient.guardian_name !== `${patient.user?.first_name} ${patient.user?.last_name}` && (
                            <span className="text-sm text-gray-500 font-normal"> ({t("admin.guardian")}: {patient.guardian_name})</span>
                          )}
                        </CardTitle>
                        <div className="flex items-center space-x-3">
                          <Badge className="bg-app-purple text-white">
                            {t("autismLevels." + patient.autism_level)}
                          </Badge>
                          {patient.therapist && (
                            <Badge variant="outline" className="border-app-green text-app-green">
                              {patient.therapist.first_name} {patient.therapist.last_name}
                            </Badge>
                          )}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="flex items-center space-x-2 text-sm text-gray-600 mb-1">
                          <Calendar className="h-4 w-4" />
                          <span>{patient.date_of_birth ? new Date(patient.date_of_birth).toLocaleDateString() : t("admin.notSpecified")}</span>
                        </div>
                        <span className="text-xs text-gray-500">
                          {t("admin.registered")}: {new Date(patient.created_at).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="flex items-center justify-between">
                      <div className="text-sm text-gray-600">
                        {patient.assigned_therapist_id ? (
                          <span className="text-app-green">{t("admin.assignedToTherapist")}</span>
                        ) : (
                          <span className="text-orange-600">{t("admin.awaitingTherapistAssignment")}</span>
                        )}
                      </div>
                      <div className="flex space-x-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleViewProfile(patient)}
                          className="rounded-xl border-app-purple text-app-purple hover:bg-app-purple hover:text-white"
                        >
                          {t("admin.viewProfile")}
                        </Button>
                        <Button 
                          size="sm"
                          onClick={() => handleManage(patient)}
                          className="bg-app-purple hover:bg-app-purple/90 text-white rounded-xl"
                        >
                          {t("admin.manage")}
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>

          {/* Bottom spacing */}
          <div className="h-8"></div>
        </div>

        {/* Patient Management Modal */}
        <PatientManageModal
          isOpen={showManageModal}
          onClose={() => setShowManageModal(false)}
          patient={selectedPatient}
          therapists={therapists}
          onUpdate={fetchData}
        />
      </div>
    </Layout>
  );
}
