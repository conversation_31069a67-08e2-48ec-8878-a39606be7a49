
import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/useAuth';
import { useAuthFlow } from '@/hooks/useAuthFlow';
import { Users, UserCheck } from 'lucide-react';

interface ProfileCompletionProps {
  userType: 'patient' | 'therapist';
}

export function ProfileCompletion({ userType }: ProfileCompletionProps) {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<Record<string, any>>({});
  const { toast } = useToast();
  const { profile } = useAuth();
  const { completeProfile } = useAuthFlow();

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async () => {
    setLoading(true);
    try {
      const success = await completeProfile(formData);
      if (success) {
        toast({
          title: 'Profile Completed',
          description: 'Your profile has been completed successfully!',
        });
        window.location.reload(); // Refresh to update auth state
      } else {
        throw new Error('Failed to complete profile');
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to complete profile. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const renderPatientForm = () => (
    <div className="space-y-4">
      <Input
        placeholder="Guardian Name"
        value={formData.guardian_name || ''}
        onChange={(e) => handleInputChange('guardian_name', e.target.value)}
        className="h-12"
      />
      <Input
        type="date"
        placeholder="Date of Birth"
        value={formData.date_of_birth || ''}
        onChange={(e) => handleInputChange('date_of_birth', e.target.value)}
        className="h-12"
      />
      <Select 
        value={formData.autism_level || ''} 
        onValueChange={(value) => handleInputChange('autism_level', value)}
      >
        <SelectTrigger className="h-12">
          <SelectValue placeholder="Autism Level" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="1">Level 1 - Requiring Support</SelectItem>
          <SelectItem value="2">Level 2 - Requiring Substantial Support</SelectItem>
          <SelectItem value="3">Level 3 - Requiring Very Substantial Support</SelectItem>
        </SelectContent>
      </Select>
      <Input
        placeholder="Emergency Contact"
        value={formData.emergency_contact || ''}
        onChange={(e) => handleInputChange('emergency_contact', e.target.value)}
        className="h-12"
      />
    </div>
  );

  const renderTherapistForm = () => (
    <div className="space-y-4">
      <Input
        placeholder="License Number (Optional)"
        value={formData.license_number || ''}
        onChange={(e) => handleInputChange('license_number', e.target.value)}
        className="h-12"
      />
      <Input
        placeholder="Specialization (Optional)"
        value={formData.specialization || ''}
        onChange={(e) => handleInputChange('specialization', e.target.value)}
        className="h-12"
      />
      <Input
        placeholder="Years of Experience (Optional)"
        type="number"
        value={formData.years_experience || ''}
        onChange={(e) => handleInputChange('years_experience', e.target.value)}
        className="h-12"
      />
    </div>
  );

  const config = userType === 'patient' 
    ? { icon: Users, color: 'text-app-purple', bgColor: 'bg-app-purple' }
    : { icon: UserCheck, color: 'text-app-green', bgColor: 'bg-app-green' };

  const IconComponent = config.icon;

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <Card className="bg-white border-0 shadow-xl rounded-2xl overflow-hidden">
          <CardHeader className="text-center pb-4">
            <div className="flex items-center justify-center mb-4">
              <div className={`p-4 ${config.bgColor} rounded-2xl`}>
                <IconComponent className="h-8 w-8 text-white" />
              </div>
            </div>
            <CardTitle className={`text-2xl font-bold ${config.color}`}>
              Complete Your Profile
            </CardTitle>
            <CardDescription className="text-gray-600">
              Welcome {profile?.first_name}! Please complete your profile to get started.
            </CardDescription>
          </CardHeader>
          <CardContent className="px-8 pb-8">
            {userType === 'patient' ? renderPatientForm() : renderTherapistForm()}
            
            <Button 
              onClick={handleSubmit}
              className={`w-full h-12 mt-6 ${config.bgColor} hover:${config.bgColor}/90 text-white`}
              disabled={loading}
            >
              {loading ? 'Completing Profile...' : 'Complete Profile'}
            </Button>
            
            <div className="mt-4 text-center">
              <Button 
                variant="ghost" 
                onClick={() => window.location.href = '/dashboard'}
                className="text-gray-500 hover:text-gray-700"
              >
                Skip for now
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
