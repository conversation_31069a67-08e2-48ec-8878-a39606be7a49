export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)"
  }
  public: {
    Tables: {
      achievements: {
        Row: {
          achieved_date: string
          category: string
          created_at: string
          description: string | null
          id: string
          patient_id: string
          title: string
        }
        Insert: {
          achieved_date?: string
          category: string
          created_at?: string
          description?: string | null
          id?: string
          patient_id: string
          title: string
        }
        Update: {
          achieved_date?: string
          category?: string
          created_at?: string
          description?: string | null
          id?: string
          patient_id?: string
          title?: string
        }
        Relationships: [
          {
            foreignKeyName: "achievements_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "patients"
            referencedColumns: ["id"]
          },
        ]
      }
      activity_completions: {
        Row: {
          activity_id: string
          completed: boolean
          completed_at: string | null
          completion_date: string
          created_at: string
          id: string
          notes: string | null
          patient_id: string
        }
        Insert: {
          activity_id: string
          completed?: boolean
          completed_at?: string | null
          completion_date?: string
          created_at?: string
          id?: string
          notes?: string | null
          patient_id: string
        }
        Update: {
          activity_id?: string
          completed?: boolean
          completed_at?: string | null
          completion_date?: string
          created_at?: string
          id?: string
          notes?: string | null
          patient_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "activity_completions_activity_id_fkey"
            columns: ["activity_id"]
            isOneToOne: false
            referencedRelation: "daily_activities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "activity_completions_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "patients"
            referencedColumns: ["id"]
          },
        ]
      }
      auth_flows: {
        Row: {
          created_at: string | null
          flow_type: string
          google_auth: boolean | null
          id: string
          profile_completed: boolean | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          flow_type: string
          google_auth?: boolean | null
          id?: string
          profile_completed?: boolean | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          flow_type?: string
          google_auth?: boolean | null
          id?: string
          profile_completed?: boolean | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      chat_messages: {
        Row: {
          chat_session_id: string
          content: string
          created_at: string
          id: string
          is_user: boolean
        }
        Insert: {
          chat_session_id: string
          content: string
          created_at?: string
          id?: string
          is_user?: boolean
        }
        Update: {
          chat_session_id?: string
          content?: string
          created_at?: string
          id?: string
          is_user?: boolean
        }
        Relationships: [
          {
            foreignKeyName: "chat_messages_chat_session_id_fkey"
            columns: ["chat_session_id"]
            isOneToOne: false
            referencedRelation: "chat_sessions"
            referencedColumns: ["id"]
          },
        ]
      }
      chat_sessions: {
        Row: {
          created_at: string
          id: string
          mode: string
          patient_id: string
          title: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          mode: string
          patient_id: string
          title: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          mode?: string
          patient_id?: string
          title?: string
          updated_at?: string
        }
        Relationships: []
      }
      daily_activities: {
        Row: {
          category: string | null
          created_at: string
          description: string | null
          end_date: string | null
          id: string
          is_active: boolean | null
          patient_id: string
          scheduled_time: string | null
          start_date: string | null
          therapist_id: string
          title: string
          updated_at: string
        }
        Insert: {
          category?: string | null
          created_at?: string
          description?: string | null
          end_date?: string | null
          id?: string
          is_active?: boolean | null
          patient_id: string
          scheduled_time?: string | null
          start_date?: string | null
          therapist_id: string
          title: string
          updated_at?: string
        }
        Update: {
          category?: string | null
          created_at?: string
          description?: string | null
          end_date?: string | null
          id?: string
          is_active?: boolean | null
          patient_id?: string
          scheduled_time?: string | null
          start_date?: string | null
          therapist_id?: string
          title?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "daily_activities_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "patients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "daily_activities_therapist_id_fkey"
            columns: ["therapist_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      milestones: {
        Row: {
          achieved_date: string | null
          category: string
          created_at: string
          description: string | null
          id: string
          notes: string | null
          patient_id: string
          regression_date: string | null
          status: Database["public"]["Enums"]["milestone_status"]
          title: string
          updated_at: string
        }
        Insert: {
          achieved_date?: string | null
          category: string
          created_at?: string
          description?: string | null
          id?: string
          notes?: string | null
          patient_id: string
          regression_date?: string | null
          status?: Database["public"]["Enums"]["milestone_status"]
          title: string
          updated_at?: string
        }
        Update: {
          achieved_date?: string | null
          category?: string
          created_at?: string
          description?: string | null
          id?: string
          notes?: string | null
          patient_id?: string
          regression_date?: string | null
          status?: Database["public"]["Enums"]["milestone_status"]
          title?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "milestones_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "patients"
            referencedColumns: ["id"]
          },
        ]
      }
      observations: {
        Row: {
          category: string | null
          created_at: string
          id: string
          observation_date: string
          observation_text: string
          patient_id: string
          session_id: string | null
          therapist_id: string
        }
        Insert: {
          category?: string | null
          created_at?: string
          id?: string
          observation_date?: string
          observation_text: string
          patient_id: string
          session_id?: string | null
          therapist_id: string
        }
        Update: {
          category?: string | null
          created_at?: string
          id?: string
          observation_date?: string
          observation_text?: string
          patient_id?: string
          session_id?: string | null
          therapist_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "observations_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "patients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "observations_session_id_fkey"
            columns: ["session_id"]
            isOneToOne: false
            referencedRelation: "therapy_sessions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "observations_therapist_id_fkey"
            columns: ["therapist_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      patients: {
        Row: {
          assigned_therapist_id: string | null
          autism_level: Database["public"]["Enums"]["autism_level"]
          created_at: string
          date_of_birth: string | null
          emergency_contact: string | null
          guardian_email: string | null
          guardian_name: string | null
          guardian_phone: string | null
          id: string
          medical_notes: string | null
          updated_at: string
          user_id: string | null
        }
        Insert: {
          assigned_therapist_id?: string | null
          autism_level: Database["public"]["Enums"]["autism_level"]
          created_at?: string
          date_of_birth?: string | null
          emergency_contact?: string | null
          guardian_email?: string | null
          guardian_name?: string | null
          guardian_phone?: string | null
          id?: string
          medical_notes?: string | null
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          assigned_therapist_id?: string | null
          autism_level?: Database["public"]["Enums"]["autism_level"]
          created_at?: string
          date_of_birth?: string | null
          emergency_contact?: string | null
          guardian_email?: string | null
          guardian_name?: string | null
          guardian_phone?: string | null
          id?: string
          medical_notes?: string | null
          updated_at?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "patients_assigned_therapist_id_fkey"
            columns: ["assigned_therapist_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "patients_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          created_at: string
          email: string
          first_name: string | null
          google_id: string | null
          id: string
          last_name: string | null
          role: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          email: string
          first_name?: string | null
          google_id?: string | null
          id: string
          last_name?: string | null
          role?: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          email?: string
          first_name?: string | null
          google_id?: string | null
          id?: string
          last_name?: string | null
          role?: string
          updated_at?: string
        }
        Relationships: []
      }
      therapy_goals: {
        Row: {
          category: string
          created_at: string
          description: string | null
          id: string
          is_long_term: boolean | null
          patient_id: string
          progress_percentage: number | null
          simple_status: string
          status: Database["public"]["Enums"]["goal_status"]
          target_date: string | null
          therapist_id: string
          therapy_type: string | null
          title: string
          updated_at: string
        }
        Insert: {
          category: string
          created_at?: string
          description?: string | null
          id?: string
          is_long_term?: boolean | null
          patient_id: string
          progress_percentage?: number | null
          simple_status?: string
          status?: Database["public"]["Enums"]["goal_status"]
          target_date?: string | null
          therapist_id: string
          therapy_type?: string | null
          title: string
          updated_at?: string
        }
        Update: {
          category?: string
          created_at?: string
          description?: string | null
          id?: string
          is_long_term?: boolean | null
          patient_id?: string
          progress_percentage?: number | null
          simple_status?: string
          status?: Database["public"]["Enums"]["goal_status"]
          target_date?: string | null
          therapist_id?: string
          therapy_type?: string | null
          title?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "therapy_goals_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "patients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "therapy_goals_therapist_id_fkey"
            columns: ["therapist_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      therapy_sessions: {
        Row: {
          created_at: string
          duration_minutes: number
          focus_areas: string | null
          id: string
          is_virtual: boolean | null
          meeting_link: string | null
          observations: string | null
          patient_id: string
          scheduled_datetime: string
          session_notes: string | null
          session_type: Database["public"]["Enums"]["session_type"]
          status: Database["public"]["Enums"]["session_status"]
          therapist_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          duration_minutes?: number
          focus_areas?: string | null
          id?: string
          is_virtual?: boolean | null
          meeting_link?: string | null
          observations?: string | null
          patient_id: string
          scheduled_datetime: string
          session_notes?: string | null
          session_type: Database["public"]["Enums"]["session_type"]
          status?: Database["public"]["Enums"]["session_status"]
          therapist_id: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          duration_minutes?: number
          focus_areas?: string | null
          id?: string
          is_virtual?: boolean | null
          meeting_link?: string | null
          observations?: string | null
          patient_id?: string
          scheduled_datetime?: string
          session_notes?: string | null
          session_type?: Database["public"]["Enums"]["session_type"]
          status?: Database["public"]["Enums"]["session_status"]
          therapist_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "therapy_sessions_patient_id_fkey"
            columns: ["patient_id"]
            isOneToOne: false
            referencedRelation: "patients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "therapy_sessions_therapist_id_fkey"
            columns: ["therapist_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      therapy_types: {
        Row: {
          created_at: string
          default_duration_minutes: number | null
          description: string | null
          id: string
          is_active: boolean | null
          name: string
        }
        Insert: {
          created_at?: string
          default_duration_minutes?: number | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          name: string
        }
        Update: {
          created_at?: string
          default_duration_minutes?: number | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          name?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      complete_profile: {
        Args: { p_user_id: string; p_additional_data?: Json }
        Returns: boolean
      }
      get_safe_user_role: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      is_assigned_therapist: {
        Args: { patient_id: string }
        Returns: boolean
      }
      is_patient_owner: {
        Args: { patient_id: string }
        Returns: boolean
      }
    }
    Enums: {
      autism_level: "1" | "2" | "3"
      goal_status: "active" | "completed" | "paused" | "archived"
      milestone_status:
        | "completed"
        | "in_progress"
        | "regressed"
        | "not_started"
      session_status:
        | "scheduled"
        | "confirmed"
        | "completed"
        | "cancelled"
        | "rescheduled"
      session_type:
        | "aba_therapy"
        | "speech_therapy"
        | "occupational_therapy"
        | "behavioral_therapy"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      autism_level: ["1", "2", "3"],
      goal_status: ["active", "completed", "paused", "archived"],
      milestone_status: [
        "completed",
        "in_progress",
        "regressed",
        "not_started",
      ],
      session_status: [
        "scheduled",
        "confirmed",
        "completed",
        "cancelled",
        "rescheduled",
      ],
      session_type: [
        "aba_therapy",
        "speech_therapy",
        "occupational_therapy",
        "behavioral_therapy",
      ],
    },
  },
} as const
