
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './useAuth';

interface AuthFlow {
  id: string;
  flow_type: 'patient' | 'therapist' | 'admin';
  google_auth: boolean;
  profile_completed: boolean;
}

export function useAuthFlow() {
  const [authFlow, setAuthFlow] = useState<AuthFlow | null>(null);
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();

  useEffect(() => {
    if (!user) {
      setAuthFlow(null);
      setLoading(false);
      return;
    }

    fetchAuthFlow();
  }, [user]);

  const fetchAuthFlow = async () => {
    try {
      const { data, error } = await supabase
        .from('auth_flows')
        .select('*')
        .eq('user_id', user?.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error fetching auth flow:', error);
        return;
      }

      // Type assertion to ensure flow_type matches our expected union type
      if (data) {
        const authFlowData: AuthFlow = {
          id: data.id,
          flow_type: data.flow_type as 'patient' | 'therapist' | 'admin',
          google_auth: data.google_auth,
          profile_completed: data.profile_completed
        };
        setAuthFlow(authFlowData);
      } else {
        setAuthFlow(null);
      }
    } catch (error) {
      console.error('Error fetching auth flow:', error);
    } finally {
      setLoading(false);
    }
  };

  const completeProfile = async (additionalData?: Record<string, any>) => {
    if (!user) return false;

    try {
      const { error } = await supabase.rpc('complete_profile', {
        p_user_id: user.id,
        p_additional_data: additionalData || {}
      });

      if (error) throw error;

      await fetchAuthFlow();
      return true;
    } catch (error) {
      console.error('Error completing profile:', error);
      return false;
    }
  };

  return {
    authFlow,
    loading,
    completeProfile,
    refresh: fetchAuthFlow
  };
}
