import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { TrendingUp, TrendingDown, Users, Calendar, Target, Activity, Sparkles } from "lucide-react";
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON>hart, Pie, Cell } from "recharts";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { Layout } from "@/components/Layout";
import { useTranslation } from "react-i18next";

interface SystemStats {
  totalPatients: number;
  totalTherapists: number;
  totalSessions: number;
  completedGoals: number;
  activeGoals: number;
  completedActivities: number;
  totalActivities: number;
  milestonesCompleted: number;
  milestonesRegressed: number;
}

interface ChartData {
  name: string;
  value: number;
  color?: string;
}

const COLORS = ['#8B5CF6', '#F59E0B', '#10B981', '#EF4444', '#06B6D4'];

export default function SystemReports() {
  const [stats, setStats] = useState<SystemStats>({
    totalPatients: 0,
    totalTherapists: 0,
    totalSessions: 0,
    completedGoals: 0,
    activeGoals: 0,
    completedActivities: 0,
    totalActivities: 0,
    milestonesCompleted: 0,
    milestonesRegressed: 0,
  });
  const [sessionData, setSessionData] = useState<ChartData[]>([]);
  const [goalStatusData, setGoalStatusData] = useState<ChartData[]>([]);
  const [loading, setLoading] = useState(true);
  const [aiInsights, setAiInsights] = useState<string>("");
  const { toast } = useToast();
  const { t } = useTranslation();

  useEffect(() => {
    fetchSystemStats();
  }, []);

  useEffect(() => {
    if (stats.totalPatients > 0) {
      generateAIInsights();
    }
  }, [stats]);

  const generateAIInsights = async () => {
    try {
      const systemData = {
        totalPatients: stats.totalPatients,
        activeTherapists: stats.totalTherapists,
        sessionsThisWeek: stats.totalSessions,
        progressRate: calculateProgress(stats.completedGoals, stats.completedGoals + stats.activeGoals),
        goalCompletionRate: calculateProgress(stats.completedGoals, stats.completedGoals + stats.activeGoals),
        activityCompletionRate: calculateProgress(stats.completedActivities, stats.totalActivities),
      };

      const response = await supabase.functions.invoke('generate-admin-reports', {
        body: { systemData }
      });

      if (response.data?.insight) {
        // Keep insights very short - just first sentence
        const shortInsight = response.data.insight.split('.')[0] + '.';
        setAiInsights(shortInsight);
      }
    } catch (error) {
      console.log("AI insights unavailable:", error);
    }
  };

  const fetchSystemStats = async () => {
    try {
      // Fetch all stats in parallel
      const [
        patientsResult,
        therapistsResult,
        sessionsResult,
        goalsResult,
        activitiesResult,
        completionsResult,
        milestonesResult,
      ] = await Promise.all([
        supabase.from("patients").select("*", { count: "exact", head: true }),
        supabase.from("profiles").select("*", { count: "exact", head: true }).eq("role", "therapist"),
        supabase.from("therapy_sessions").select("*", { count: "exact", head: true }),
        supabase.from("therapy_goals").select("status"),
        supabase.from("daily_activities").select("*", { count: "exact", head: true }),
        supabase.from("activity_completions").select("completed"),
        supabase.from("milestones").select("status"),
      ]);

      // Process goals data
      const completedGoals = goalsResult.data?.filter(g => g.status === "completed").length || 0;
      const activeGoals = goalsResult.data?.filter(g => g.status === "active").length || 0;

      // Process activity completions
      const completedActivities = completionsResult.data?.filter(c => c.completed).length || 0;

      // Process milestones data
      const milestonesCompleted = milestonesResult.data?.filter(m => m.status === "completed").length || 0;
      const milestonesRegressed = milestonesResult.data?.filter(m => m.status === "regressed").length || 0;

      setStats({
        totalPatients: patientsResult.count || 0,
        totalTherapists: therapistsResult.count || 0,
        totalSessions: sessionsResult.count || 0,
        completedGoals,
        activeGoals,
        completedActivities,
        totalActivities: activitiesResult.count || 0,
        milestonesCompleted,
        milestonesRegressed,
      });

      // Prepare chart data
      const goalStatusCounts = goalsResult.data?.reduce((acc: any, goal) => {
        acc[goal.status] = (acc[goal.status] || 0) + 1;
        return acc;
      }, {}) || {};

      setGoalStatusData(
        Object.entries(goalStatusCounts).map(([status, count], index) => ({
          name: t(`status.${status}`),
          value: count as number,
          color: COLORS[index % COLORS.length],
        }))
      );

      // Mock session data by month (replace with actual date-based queries)
      setSessionData([
        { name: t("months.january"), value: 12 },
        { name: t("months.february"), value: 15 },
        { name: t("months.march"), value: 18 },
        { name: t("months.april"), value: 22 },
        { name: t("months.may"), value: 20 },
        { name: t("months.june"), value: 25 },
      ]);
    } catch (error: any) {
      toast({
        title: t("common.error"),
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const calculateProgress = (completed: number, total: number) => {
    return total > 0 ? Math.round((completed / total) * 100) : 0;
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-app-purple"></div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto space-y-8">
          {/* Header */}
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-2">
              {t("admin.systemReports")}
            </h1>
            <p className="text-lg text-gray-600">{t("admin.comprehensiveAnalytics")}</p>
          </div>

          {/* AI Insights Card */}
          {aiInsights && (
            <Card className="bg-app-purple text-white border-0 shadow-lg rounded-2xl">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Sparkles className="h-6 w-6" />
                  <span>{t("admin.aiSystemAnalysis")}</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-white/90">{aiInsights}</p>
              </CardContent>
            </Card>
          )}

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="bg-white border-0 shadow-lg rounded-2xl">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">{t("admin.totalPatients")}</CardTitle>
                <div className="p-2 bg-app-purple/10 rounded-xl">
                  <Users className="h-4 w-4 text-app-purple" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-app-purple">{stats.totalPatients}</div>
                <div className="flex items-center text-xs text-app-green">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  {t("admin.fromLastMonth", { count: 2 })}
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white border-0 shadow-lg rounded-2xl">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">{t("admin.activeTherapists")}</CardTitle>
                <div className="p-2 bg-blue-100 rounded-xl">
                  <Users className="h-4 w-4 text-blue-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">{stats.totalTherapists}</div>
                <div className="flex items-center text-xs text-app-green">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  {t("admin.fromLastMonth", { count: 1 })}
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white border-0 shadow-lg rounded-2xl">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">{t("admin.totalSessions")}</CardTitle>
                <div className="p-2 bg-app-green/10 rounded-xl">
                  <Calendar className="h-4 w-4 text-app-green" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-app-green">{stats.totalSessions}</div>
                <div className="flex items-center text-xs text-app-green">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  {t("admin.thisWeekIncrease")}
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white border-0 shadow-lg rounded-2xl">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">{t("admin.goalCompletion")}</CardTitle>
                <div className="p-2 bg-app-yellow/10 rounded-xl">
                  <Target className="h-4 w-4 text-app-yellow" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-app-yellow">
                  {calculateProgress(stats.completedGoals, stats.completedGoals + stats.activeGoals)}%
                </div>
                <div className="flex items-center text-xs text-app-green">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  {t("admin.percentFromLastMonth", { percent: 5 })}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Progress Overview */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="bg-white border-0 shadow-lg rounded-2xl">
              <CardHeader>
                <CardTitle>{t("admin.therapyGoalsProgress")}</CardTitle>
                <CardDescription>{t("admin.goalCompletionOverview")}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>{t("admin.completedGoals")}</span>
                    <span>{stats.completedGoals}/{stats.completedGoals + stats.activeGoals}</span>
                  </div>
                  <Progress 
                    value={calculateProgress(stats.completedGoals, stats.completedGoals + stats.activeGoals)} 
                    className="h-2"
                  />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>{t("admin.activityCompletion")}</span>
                    <span>{stats.completedActivities}/{stats.totalActivities}</span>
                  </div>
                  <Progress 
                    value={calculateProgress(stats.completedActivities, stats.totalActivities)} 
                    className="h-2"
                  />
                </div>
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>{t("admin.milestonesAchieved")}</span>
                    <span>{stats.milestonesCompleted}</span>
                  </div>
                  <Progress 
                    value={85} 
                    className="h-2"
                  />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white border-0 shadow-lg rounded-2xl">
              <CardHeader>
                <CardTitle>{t("admin.milestoneStatus")}</CardTitle>
                <CardDescription>{t("admin.milestoneProgressDistribution")}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-app-green">{stats.milestonesCompleted}</div>
                    <div className="text-sm text-gray-600">{t("status.completed")}</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">{stats.milestonesRegressed}</div>
                    <div className="text-sm text-gray-600">{t("admin.regressed")}</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="bg-white border-0 shadow-lg rounded-2xl">
              <CardHeader>
                <CardTitle>{t("admin.sessionTrends")}</CardTitle>
                <CardDescription>{t("admin.monthlySessionDistribution")}</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={sessionData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="value" fill="#8B5CF6" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card className="bg-white border-0 shadow-lg rounded-2xl">
              <CardHeader>
                <CardTitle>{t("admin.goalStatusDistribution")}</CardTitle>
                <CardDescription>{t("admin.currentGoalStatusBreakdown")}</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={goalStatusData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {goalStatusData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color || COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="bg-white border-0 shadow-lg rounded-2xl">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Activity className="h-5 w-5 text-app-purple" />
                  <span>{t("admin.activityCompliance")}</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-app-purple">
                  {calculateProgress(stats.completedActivities, stats.totalActivities)}%
                </div>
                <p className="text-sm text-gray-600 mt-2">
                  {stats.completedActivities} of {stats.totalActivities} activities completed
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white border-0 shadow-lg rounded-2xl">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Target className="h-5 w-5 text-app-green" />
                  <span>{t("admin.goalAchievement")}</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-app-green">
                  {calculateProgress(stats.completedGoals, stats.completedGoals + stats.activeGoals)}%
                </div>
                <p className="text-sm text-gray-600 mt-2">
                  {stats.completedGoals} goals completed successfully
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white border-0 shadow-lg rounded-2xl">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <TrendingUp className="h-5 w-5 text-blue-600" />
                  <span>{t("admin.systemGrowth")}</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-blue-600">+15%</div>
                <p className="text-sm text-gray-600 mt-2">
                  Overall system usage increased this month
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </Layout>
  );
}
