
import { useState, useCallback } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/hooks/useAuth";

interface TherapySession {
  id: string;
  scheduled_datetime: string;
  duration_minutes: number;
  session_type: string;
  is_virtual: boolean;
  observations: string;
  therapist: {
    first_name: string;
    last_name: string;
  } | null;
}

export function useSessionData() {
  const [sessionDetails, setSessionDetails] = useState<TherapySession | null>(null);
  const { profile } = useAuth();

  const fetchSessionForDate = useCallback(async (date: Date) => {
    try {
      console.log("Fetching session for date:", date);
      
      let { data: patientData, error: patientError } = await supabase
        .from("patients")
        .select("id")
        .eq("user_id", profile?.id)
        .maybeSingle();

      console.log("Patient data for session:", patientData, "Error:", patientError);

      if (patientError || !patientData) {
        console.log("No patient found, setting session to null");
        setSessionDetails(null);
        return;
      }

      const startOfDay = new Date(date);
      startOfDay.setHours(0, 0, 0, 0);
      
      const endOfDay = new Date(date);
      endOfDay.setHours(23, 59, 59, 999);

      console.log("Querying sessions between:", startOfDay.toISOString(), "and", endOfDay.toISOString());

      const { data: sessionData, error: sessionError } = await supabase
        .from("therapy_sessions")
        .select(`
          id,
          scheduled_datetime,
          duration_minutes,
          session_type,
          is_virtual,
          observations,
          therapist_id
        `)
        .eq("patient_id", patientData.id)
        .gte("scheduled_datetime", startOfDay.toISOString())
        .lte("scheduled_datetime", endOfDay.toISOString())
        .maybeSingle();

      console.log("Session data fetched:", sessionData, "Error:", sessionError);

      if (!sessionError && sessionData) {
        // Now fetch the therapist details separately
        const { data: therapistData, error: therapistError } = await supabase
          .from("profiles")
          .select("first_name, last_name")
          .eq("id", sessionData.therapist_id)
          .maybeSingle();

        console.log("Therapist data fetched:", therapistData, "Error:", therapistError);

        const transformedSession = {
          ...sessionData,
          therapist: therapistData || null
        };
        
        console.log("Setting session details with therapist:", transformedSession);
        setSessionDetails(transformedSession);
      } else {
        console.log("No session found for this date");
        setSessionDetails(null);
      }
    } catch (error) {
      console.error("Error fetching session for date:", error);
      setSessionDetails(null);
    }
  }, [profile?.id]);

  return {
    sessionDetails,
    fetchSessionForDate
  };
}
