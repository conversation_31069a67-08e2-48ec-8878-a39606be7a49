
-- Drop existing messaging tables to start fresh
DROP TABLE IF EXISTS public.messages CASCADE;
DROP TABLE IF EXISTS public.video_sessions CASCADE;

-- Create a new realtime_messages table for therapist-patient communication
CREATE TABLE public.realtime_messages (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  sender_id UUID REFERENCES auth.users NOT NULL,
  recipient_id UUID REFERENCES auth.users NOT NULL,
  content TEXT NOT NULL,
  message_type TEXT DEFAULT 'text' CHECK (message_type IN ('text', 'file', 'image')),
  file_url TEXT,
  is_read BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS on realtime_messages table
ALTER TABLE public.realtime_messages ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for realtime_messages
CREATE POLICY "Users can view messages they sent or received" 
  ON public.realtime_messages 
  FOR SELECT 
  USING (auth.uid() = sender_id OR auth.uid() = recipient_id);

CREATE POLICY "Users can send messages" 
  ON public.realtime_messages 
  FOR INSERT 
  WITH CHECK (auth.uid() = sender_id);

CREATE POLICY "Users can update their own messages" 
  ON public.realtime_messages 
  FOR UPDATE 
  USING (auth.uid() = sender_id OR auth.uid() = recipient_id);

-- Add trigger for updated_at
CREATE TRIGGER handle_updated_at_realtime_messages
  BEFORE UPDATE ON public.realtime_messages
  FOR EACH ROW EXECUTE PROCEDURE public.handle_updated_at();

-- Create indexes for performance
CREATE INDEX idx_realtime_messages_sender_recipient ON public.realtime_messages(sender_id, recipient_id);
CREATE INDEX idx_realtime_messages_created_at ON public.realtime_messages(created_at DESC);
CREATE INDEX idx_realtime_messages_unread ON public.realtime_messages(recipient_id, is_read);

-- Enable realtime for the table
ALTER TABLE public.realtime_messages REPLICA IDENTITY FULL;
ALTER PUBLICATION supabase_realtime ADD TABLE public.realtime_messages;
