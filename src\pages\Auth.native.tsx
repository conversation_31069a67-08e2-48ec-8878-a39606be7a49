import React, { useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, SafeAreaView } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '@/hooks/useAuth';

export default function AuthNative() {
  const navigation = useNavigation();
  const { isAuthenticated } = useAuth();

  useEffect(() => {
    if (isAuthenticated) {
      navigation.navigate('Dashboard' as never);
    }
  }, [isAuthenticated, navigation]);

  const authRoutes = [
    {
      type: 'patient',
      title: 'Patient Portal',
      description: 'Access your therapy goals and activities',
      color: '#8B5CF6',
      route: 'PatientAuth'
    },
    {
      type: 'therapist',
      title: 'Therapist Portal', 
      description: 'Manage patients and therapy sessions',
      color: '#10B981',
      route: 'TherapistAuth'
    },
    {
      type: 'admin',
      title: 'Admin Portal',
      description: 'System administration and reports',
      color: '#F59E0B',
      route: 'AdminAuth'
    }
  ];

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.title}>Happy Hi Galaxy</Text>
          <Text style={styles.subtitle}>Choose Your Access Type</Text>
        </View>

        <View style={styles.buttonContainer}>
          {authRoutes.map((route) => (
            <TouchableOpacity
              key={route.type}
              style={[styles.authButton, { backgroundColor: route.color }]}
              onPress={() => navigation.navigate(route.route as never)}
            >
              <Text style={styles.buttonTitle}>{route.title}</Text>
              <Text style={styles.buttonDescription}>{route.description}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 18,
    color: '#6B7280',
    textAlign: 'center',
  },
  buttonContainer: {
    gap: 16,
  },
  authButton: {
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
  },
  buttonTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 4,
  },
  buttonDescription: {
    fontSize: 14,
    color: 'white',
    opacity: 0.9,
    textAlign: 'center',
  },
});
