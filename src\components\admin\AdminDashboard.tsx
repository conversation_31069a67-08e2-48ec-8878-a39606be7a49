import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Users, Target, ClipboardList, Calendar, TrendingUp, Plus, Sparkles } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { Skeleton } from "@/components/ui/skeleton";

interface DashboardStats {
  totalPatients: number;
  activeTherapists: number;
  sessionsThisWeek: number;
  progressRate: number;
  patientGrowth: number;
  therapistGrowth: number;
  sessionGrowth: number;
  progressGrowth: number;
}

export function AdminDashboard() {
  const navigate = useNavigate();
  const { profile } = useAuth();
  const { toast } = useToast();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [aiInsights, setAiInsights] = useState<string>("");

  useEffect(() => {
    if (profile?.id) {
      fetchDashboardStats();
    }
  }, [profile]);

  const fetchDashboardStats = async () => {
    try {
      // Get current date ranges
      const now = new Date();
      const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      const twoMonthsAgo = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000);

      // Fetch current stats
      const [patientsResponse, therapistsResponse, sessionsResponse, goalsResponse] = await Promise.all([
        supabase.from("patients").select("id, created_at"),
        supabase.from("profiles").select("id, created_at").eq("role", "therapist"),
        supabase.from("therapy_sessions").select("id, scheduled_datetime").gte("scheduled_datetime", oneWeekAgo.toISOString()),
        supabase.from("therapy_goals").select("id, progress_percentage")
      ]);

      if (patientsResponse.error) throw patientsResponse.error;
      if (therapistsResponse.error) throw therapistsResponse.error;
      if (sessionsResponse.error) throw sessionsResponse.error;
      if (goalsResponse.error) throw goalsResponse.error;

      // Calculate current stats
      const totalPatients = patientsResponse.data?.length || 0;
      const activeTherapists = therapistsResponse.data?.length || 0;
      const sessionsThisWeek = sessionsResponse.data?.length || 0;
      
      const goals = goalsResponse.data || [];
      const avgProgress = goals.length > 0 
        ? Math.round(goals.reduce((sum, goal) => sum + (goal.progress_percentage || 0), 0) / goals.length)
        : 0;

      // Calculate growth percentages
      const patientsLastMonth = patientsResponse.data?.filter(p => 
        new Date(p.created_at) >= oneMonthAgo && new Date(p.created_at) < now
      ).length || 0;
      
      const patientsThisMonth = patientsResponse.data?.filter(p => 
        new Date(p.created_at) >= oneMonthAgo
      ).length || 0;

      const therapistsLastMonth = therapistsResponse.data?.filter(t => 
        new Date(t.created_at) >= oneMonthAgo && new Date(t.created_at) < now
      ).length || 0;

      const patientGrowth = patientsLastMonth > 0 ? Math.round(((patientsThisMonth - patientsLastMonth) / patientsLastMonth) * 100) : 0;
      const therapistGrowth = therapistsLastMonth > 0 ? Math.round(((activeTherapists - therapistsLastMonth) / therapistsLastMonth) * 100) : 0;

      const dashboardStats: DashboardStats = {
        totalPatients,
        activeTherapists,
        sessionsThisWeek,
        progressRate: avgProgress,
        patientGrowth,
        therapistGrowth,
        sessionGrowth: 12, // This would need historical session data
        progressGrowth: 5   // This would need historical progress data
      };

      setStats(dashboardStats);
      
      // Generate AI insights
      generateAIInsights(dashboardStats);
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const generateAIInsights = async (statsData: DashboardStats) => {
    try {
      const response = await supabase.functions.invoke('generate-admin-reports', {
        body: { 
          stats: statsData,
          type: 'dashboard_overview'
        }
      });

      if (response.data?.insight) {
        // Keep insights very short - just first sentence
        const shortInsight = response.data.insight.split('.')[0] + '.';
        setAiInsights(shortInsight);
      }
    } catch (error) {
      console.log("AI insights unavailable:", error);
    }
  };

  if (loading) {
    return (
      <div className="space-y-8 p-4 md:p-8">
        <div className="text-center space-y-4">
          <Skeleton className="h-16 w-96 mx-auto" />
          <Skeleton className="h-6 w-64 mx-auto" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map((i) => (
            <Skeleton key={i} className="h-32" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8 p-4 md:p-8 bg-gray-50 min-h-screen">
      {/* Header Section */}
      <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
        <div className="text-center md:text-left">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            Admin Dashboard
          </h1>
          <p className="text-gray-600 text-lg">
            Manage the entire Patient Therapy System
          </p>
        </div>
        <Button 
          onClick={() => navigate("/admin/patients")}
          className="bg-app-purple hover:bg-app-purple/90 text-white font-semibold px-6 py-3 rounded-2xl shadow-lg transition-all duration-300"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Patient
        </Button>
      </div>

      {/* AI Insights Card */}
      {aiInsights && (
        <Card className="bg-app-purple text-white border-0 shadow-lg rounded-2xl">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Sparkles className="h-6 w-6" />
              <span>AI System Insights</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-white/90">{aiInsights}</p>
          </CardContent>
        </Card>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="bg-white border-0 shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-semibold text-gray-700">Total Patients</CardTitle>
            <div className="p-2 bg-app-purple/10 rounded-xl">
              <Users className="h-4 w-4 text-app-purple" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl md:text-4xl font-bold text-app-purple">
              {stats?.totalPatients || 0}
            </div>
            <p className="text-xs text-gray-500 mt-1">
              {stats?.patientGrowth ? `+${stats.patientGrowth}` : '+0'}% from last month
            </p>
          </CardContent>
        </Card>

        <Card className="bg-white border-0 shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-semibold text-gray-700">Active Therapists</CardTitle>
            <div className="p-2 bg-app-green/10 rounded-xl">
              <Target className="h-4 w-4 text-app-green" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl md:text-4xl font-bold text-app-green">
              {stats?.activeTherapists || 0}
            </div>
            <p className="text-xs text-gray-500 mt-1">
              {stats?.therapistGrowth ? `+${stats.therapistGrowth}` : '+0'}% from last month
            </p>
          </CardContent>
        </Card>

        <Card className="bg-white border-0 shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-semibold text-gray-700">Sessions This Week</CardTitle>
            <div className="p-2 bg-app-yellow/10 rounded-xl">
              <Calendar className="h-4 w-4 text-app-yellow" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl md:text-4xl font-bold text-app-yellow">
              {stats?.sessionsThisWeek || 0}
            </div>
            <p className="text-xs text-gray-500 mt-1">
              +{stats?.sessionGrowth || 0}% from last week
            </p>
          </CardContent>
        </Card>

        <Card className="bg-white border-0 shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-semibold text-gray-700">Progress Rate</CardTitle>
            <div className="p-2 bg-blue-100 rounded-xl">
              <TrendingUp className="h-4 w-4 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl md:text-4xl font-bold text-blue-600">
              {stats?.progressRate || 0}%
            </div>
            <p className="text-xs text-gray-500 mt-1">
              +{stats?.progressGrowth || 0}% from last month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Management Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="bg-white border-0 shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300 cursor-pointer" onClick={() => navigate("/admin/patients")}>
          <CardHeader>
            <CardTitle className="flex items-center space-x-3">
              <div className="p-3 bg-app-purple/10 rounded-xl">
                <Users className="h-5 w-5 text-app-purple" />
              </div>
              <span className="text-app-purple">Patient Management</span>
            </CardTitle>
            <CardDescription className="text-gray-600 mt-2">
              Manage patient profiles, assignments, and therapy progress
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button className="w-full bg-app-purple hover:bg-app-purple/90 text-white font-semibold py-3 rounded-xl shadow-lg transition-all duration-300">
              Manage Patients
            </Button>
          </CardContent>
        </Card>

        <Card className="bg-white border-0 shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300 cursor-pointer" onClick={() => navigate("/admin/therapists")}>
          <CardHeader>
            <CardTitle className="flex items-center space-x-3">
              <div className="p-3 bg-app-green/10 rounded-xl">
                <Target className="h-5 w-5 text-app-green" />
              </div>
              <span className="text-app-green">Therapist Management</span>
            </CardTitle>
            <CardDescription className="text-gray-600 mt-2">
              Oversee therapist accounts, assignments, and performance
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button className="w-full bg-app-green hover:bg-app-green/90 text-white font-semibold py-3 rounded-xl shadow-lg transition-all duration-300">
              Manage Therapists
            </Button>
          </CardContent>
        </Card>

        <Card className="bg-white border-0 shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300 cursor-pointer" onClick={() => navigate("/admin/reports")}>
          <CardHeader>
            <CardTitle className="flex items-center space-x-3">
              <div className="p-3 bg-app-yellow/10 rounded-xl">
                <ClipboardList className="h-5 w-5 text-app-yellow" />
              </div>
              <span className="text-app-yellow">System Reports</span>
            </CardTitle>
            <CardDescription className="text-gray-600 mt-2">
              Generate comprehensive reports and analytics
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button className="w-full bg-app-yellow hover:bg-app-yellow/90 text-white font-semibold py-3 rounded-xl shadow-lg transition-all duration-300">
              View Reports
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
