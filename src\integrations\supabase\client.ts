// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://jttsprfcdcoorslznlcc.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imp0dHNwcmZjZGNvb3JzbHpubGNjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkxMTUyMTAsImV4cCI6MjA2NDY5MTIxMH0.frC0HgjhbCTzElSOUoApyHXyP-0hXAFvVhkp26AjeJc";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);