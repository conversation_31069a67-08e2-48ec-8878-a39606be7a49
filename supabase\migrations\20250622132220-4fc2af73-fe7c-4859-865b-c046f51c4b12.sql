
-- Add Google provider ID to profiles table for linking Google accounts
ALTER TABLE public.profiles ADD COLUMN google_id TEXT UNIQUE;

-- Create separate authentication flows table to track different auth types
CREATE TABLE public.auth_flows (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  flow_type TEXT NOT NULL CHECK (flow_type IN ('patient', 'therapist', 'admin')),
  google_auth BOOLEAN DEFAULT FALSE,
  profile_completed BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id)
);

-- Enable RLS on auth_flows
ALTER TABLE public.auth_flows ENABLE ROW LEVEL SECURITY;

-- Policy for users to see their own auth flow
CREATE POLICY "Users can view their own auth flow" 
  ON public.auth_flows 
  FOR SELECT 
  USING (auth.uid() = user_id);

-- Policy for users to update their own auth flow
CREATE POLICY "Users can update their own auth flow" 
  ON public.auth_flows 
  FOR UPDATE 
  USING (auth.uid() = user_id);

-- Policy for users to insert their own auth flow
CREATE POLICY "Users can insert their own auth flow" 
  ON public.auth_flows 
  FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- Update the handle_new_user function to create auth_flow records
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Insert into profiles table
  INSERT INTO public.profiles (id, email, first_name, last_name, role, google_id)
  VALUES (
    NEW.id,
    NEW.email,
    NEW.raw_user_meta_data ->> 'first_name',
    NEW.raw_user_meta_data ->> 'last_name',
    COALESCE(NEW.raw_user_meta_data ->> 'role', 'patient'),
    NEW.raw_user_meta_data ->> 'provider_id'
  );

  -- Insert into auth_flows table
  INSERT INTO public.auth_flows (user_id, flow_type, google_auth, profile_completed)
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data ->> 'role', 'patient'),
    CASE WHEN NEW.raw_user_meta_data ->> 'provider' = 'google' THEN TRUE ELSE FALSE END,
    FALSE
  );

  -- If the user is a patient, create a patient record
  IF COALESCE(NEW.raw_user_meta_data ->> 'role', 'patient') = 'patient' THEN
    INSERT INTO public.patients (
      user_id,
      guardian_name,
      date_of_birth,
      autism_level,
      emergency_contact
    )
    VALUES (
      NEW.id,
      COALESCE(NEW.raw_user_meta_data ->> 'guardian_name', NEW.raw_user_meta_data ->> 'first_name' || ' ' || NEW.raw_user_meta_data ->> 'last_name'),
      COALESCE(NEW.raw_user_meta_data ->> 'date_of_birth', CURRENT_DATE::text)::date,
      COALESCE(NEW.raw_user_meta_data ->> 'autism_level', '1')::autism_level,
      NEW.raw_user_meta_data ->> 'emergency_contact'
    );
  END IF;

  RETURN NEW;
END;
$$;

-- Create function to complete profile
CREATE OR REPLACE FUNCTION public.complete_profile(
  p_user_id UUID,
  p_additional_data JSONB DEFAULT '{}'::jsonb
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_role TEXT;
BEGIN
  -- Get user role
  SELECT role INTO user_role FROM public.profiles WHERE id = p_user_id;
  
  -- Update auth_flows to mark profile as completed
  UPDATE public.auth_flows 
  SET profile_completed = TRUE, updated_at = NOW()
  WHERE user_id = p_user_id;
  
  -- Update additional profile data based on role
  IF user_role = 'patient' THEN
    -- Update patient-specific data if provided
    IF p_additional_data ? 'guardian_name' THEN
      UPDATE public.patients 
      SET guardian_name = p_additional_data ->> 'guardian_name'
      WHERE user_id = p_user_id;
    END IF;
    
    IF p_additional_data ? 'date_of_birth' THEN
      UPDATE public.patients 
      SET date_of_birth = (p_additional_data ->> 'date_of_birth')::date
      WHERE user_id = p_user_id;
    END IF;
    
    IF p_additional_data ? 'autism_level' THEN
      UPDATE public.patients 
      SET autism_level = (p_additional_data ->> 'autism_level')::autism_level
      WHERE user_id = p_user_id;
    END IF;
  END IF;
  
  RETURN TRUE;
END;
$$;
