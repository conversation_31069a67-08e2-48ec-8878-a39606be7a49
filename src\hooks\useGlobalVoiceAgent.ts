
import { useRef, useState, useCallback, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { toast } from "sonner";
import { useAuth } from "@/hooks/useAuth";

export interface GlobalVoiceCommand {
  action: string;
  target?: string;
  parameters?: Record<string, any>;
  response?: string;
  navigation?: string;
  confidence?: string;
  clarification_needed?: string;
}

interface UseGlobalVoiceAgentReturn {
  // Recording
  state: string;
  startRecording: () => void;
  stopRecording: () => void;
  reset: () => void;
  isActive: boolean;
  setActive: (a: boolean) => void;
  // Transcript/actions
  transcript: string;
  setTranscript: (t: string) => void;
  isLoading: boolean;
  setIsLoading: (l: boolean) => void;
  lastCommand: GlobalVoiceCommand | null;
  setLastCommand: (cmd: GlobalVoiceCommand | null) => void;
  // History
  history: GlobalVoiceCommand[];
  // Auto-processing
  isProcessing: boolean;
  // Context awareness
  contextState: any;
  setContextState: (ctx: any) => void;
}

export function useGlobalVoiceAgent(): UseGlobalVoiceAgentReturn {
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [state, setState] = useState("idle");
  const [isActive, setActive] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [transcript, setTranscript] = useState("");
  const [lastCommand, setLastCommand] = useState<GlobalVoiceCommand | null>(null);
  const [history, setHistory] = useState<GlobalVoiceCommand[]>([]);
  const [contextState, setContextState] = useState<any>({});
  const location = useLocation();
  const navigate = useNavigate();
  const { profile } = useAuth();
  const previousAudioUrl = useRef<string | null>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);

  // Enhanced voice recording with proper constraints
  const startRecording = useCallback(async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          sampleRate: 16000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });

      const options: MediaRecorderOptions = {
        mimeType: 'audio/wav'
      };

      if (!MediaRecorder.isTypeSupported('audio/wav')) {
        console.log('WAV not supported, falling back to webm');
        options.mimeType = 'audio/webm;codecs=opus';
      }

      const mediaRecorder = new MediaRecorder(stream, options);
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { 
          type: mediaRecorder.mimeType 
        });
        const url = URL.createObjectURL(audioBlob);
        setAudioUrl(url);
        setState("stopped");
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorder.start(1000);
      setState("recording");
      
    } catch (error) {
      console.error('Error starting recording:', error);
      toast.error("Failed to access microphone. Please check permissions.");
    }
  }, []);

  const stopRecording = useCallback(() => {
    if (mediaRecorderRef.current && state === "recording") {
      mediaRecorderRef.current.stop();
    }
  }, [state]);

  const reset = useCallback(() => {
    if (audioUrl) {
      URL.revokeObjectURL(audioUrl);
    }
    setAudioUrl(null);
    setState("idle");
    setTranscript("");
    setLastCommand(null);
    setIsProcessing(false);
    previousAudioUrl.current = null;
  }, [audioUrl]);

  // Enhanced context building
  const buildContext = useCallback(() => {
    return {
      path: location.pathname,
      role: profile?.role,
      recentCommands: history.slice(-3),
      timestamp: new Date().toISOString(),
      ...contextState
    };
  }, [location.pathname, profile?.role, history, contextState]);

  // Auto-process audio when recording stops
  const processAudioAutomatically = useCallback(async (audioUrl: string) => {
    if (!audioUrl) return;
    
    setIsProcessing(true);
    setTranscript("");
    setLastCommand(null);

    try {
      console.log("🎤 Auto-processing audio...");
      
      const blob = await fetch(audioUrl).then((res) => res.blob());
      
      if (blob.size === 0) {
        toast.error("No audio recorded. Please try speaking again.");
        return;
      }
      
      if (blob.size > 25 * 1024 * 1024) {
        toast.error("Audio file too large. Please record a shorter message.");
        return;
      }
      
      if (blob.size < 1000) {
        toast.error("Audio recording too short. Please try speaking for longer.");
        return;
      }

      // Convert to base64
      const buffer = await blob.arrayBuffer();
      let binary = "";
      const bytes = new Uint8Array(buffer);
      for (let i = 0; i < bytes.byteLength; ++i) {
        binary += String.fromCharCode(bytes[i]);
      }
      const audioBase64 = btoa(binary);

      console.log("📝 Transcribing audio...");
      
      const vtResp = await fetch("/functions/v1/voice-to-text", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ audioData: audioBase64 }),
      });
      
      const vtData = await vtResp.json();
      
      if (!vtResp.ok || vtData.error) {
        throw new Error(vtData.error || `HTTP ${vtResp.status}`);
      }
      
      const transcribedText = vtData.transcript || "";
      setTranscript(transcribedText);
      console.log("✅ Transcription:", transcribedText);

      if (!transcribedText.trim()) {
        toast.info("No speech detected. Please speak clearly and try again.");
        return;
      }

      // Parse command with context
      console.log("🤖 Parsing command with context...");
      const context = buildContext();
      
      const parserResp = await fetch("/functions/v1/therapist-command-parser", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          transcript: transcribedText,
          context
        }),
      });
      
      if (!parserResp.ok) {
        const errorText = await parserResp.text();
        throw new Error(`Parser error: ${errorText}`);
      }
      
      const parsed = await parserResp.json();
      if (parsed.error) {
        throw new Error(`Command parsing failed: ${parsed.error}`);
      }
      
      console.log("🚀 Executing command:", parsed);
      setLastCommand(parsed);
      setHistory((h) => [...h, parsed]);
      
      // Show confidence indicator
      if (parsed.confidence === "low") {
        toast.warning("Command understood with low confidence. Please confirm if this is correct.");
      } else if (parsed.confidence === "medium") {
        toast.info("Command understood. Executing...");
      } else {
        toast.success(`Command executed: ${parsed.action}`);
      }
      
      // Execute command
      executeCommand(parsed);

    } catch (err: any) {
      console.error("❌ Auto-processing error:", err);
      toast.error("Voice processing failed. Please try again.");
    } finally {
      setIsProcessing(false);
    }
  }, [buildContext, navigate]);

  // Command execution
  const executeCommand = useCallback((cmd: GlobalVoiceCommand) => {
    switch (cmd.action) {
      case "view_patients":
        navigate("/therapist/patients");
        break;
      case "view_goals":
        navigate("/therapist/goals");
        break;
      case "view_sessions":
        navigate("/therapist/sessions");
        break;
      case "view_activities":
        navigate("/therapist/activities");
        break;
      case "navigate":
        if (cmd.navigation) {
          navigate(cmd.navigation);
        }
        break;
      case "sign_out":
        window.location.href = "/auth";
        break;
    }

    if (cmd.clarification_needed) {
      toast.info(cmd.clarification_needed, { duration: 5000 });
    }

    setContextState(prev => ({
      ...prev,
      lastAction: cmd.action,
      lastPatient: cmd.parameters?.patient_name || prev.lastPatient,
      lastTimestamp: new Date().toISOString()
    }));
  }, [navigate]);

  // Watch for audioUrl changes and auto-process
  useEffect(() => {
    if (audioUrl && audioUrl !== previousAudioUrl.current && state === "stopped") {
      previousAudioUrl.current = audioUrl;
      processAudioAutomatically(audioUrl);
    }
  }, [audioUrl, state, processAudioAutomatically]);

  return {
    state,
    startRecording,
    stopRecording,
    reset,
    isActive,
    setActive,
    transcript,
    setTranscript,
    isLoading: isLoading || isProcessing,
    setIsLoading,
    lastCommand,
    setLastCommand,
    history,
    isProcessing,
    contextState,
    setContextState
  };
}
