
-- First, let's check if the trigger is properly attached to the messages table
-- and ensure we have the correct trigger function

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS trigger_pusher_message_on_insert ON public.messages;

-- Recreate the trigger to ensure it's working
CREATE TRIGGER trigger_pusher_message_on_insert
  AFTER INSERT ON public.messages
  FOR EACH ROW
  EXECUTE FUNCTION public.trigger_pusher_message();

-- Also ensure realtime is enabled for the messages table
ALTER TABLE public.messages REPLICA IDENTITY FULL;
ALTER PUBLICATION supabase_realtime ADD TABLE public.messages;
