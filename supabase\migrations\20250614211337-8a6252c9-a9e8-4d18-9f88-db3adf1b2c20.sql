
-- Drop the existing trigger and function first
DROP TRIGGER IF EXISTS on_message_insert ON messages;
DROP FUNCTION IF EXISTS trigger_pusher_message();

-- Create a simplified trigger function that uses the service role key directly
CREATE OR REPLACE FUNCTION trigger_pusher_message() 
RETURNS TRIGGER 
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql AS $$
BEGIN
  -- Call the edge function asynchronously with a direct service role key
  PERFORM
    net.http_post(
      url := 'https://jttsprfcdcoorslznlcc.supabase.co/functions/v1/trigger-pusher-message',
      headers := jsonb_build_object(
        'Content-Type', 'application/json',
        'Authorization', 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imp0dHNwcmZjZGNvb3JzbHpubGNjIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTExNTIxMCwiZXhwIjoyMDY0NjkxMjEwfQ.Z8u5FQHnGXlgVEWCO4WLxVkVHnk_5oK8eFQHK-gZxh0'
      ),
      body := jsonb_build_object(
        'message_id', NEW.id,
        'sender_id', NEW.sender_id,
        'recipient_id', NEW.recipient_id,
        'content', NEW.content,
        'message_type', NEW.message_type,
        'file_url', NEW.file_url,
        'is_read', NEW.is_read,
        'created_at', NEW.created_at
      )
    );
  
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error but don't fail the insert
    RAISE WARNING 'Failed to trigger Pusher message: %', SQLERRM;
    RETURN NEW;
END;
$$;

-- Create the trigger that fires after each message insert
CREATE TRIGGER on_message_insert
  AFTER INSERT ON messages
  FOR EACH ROW
  EXECUTE FUNCTION trigger_pusher_message();
