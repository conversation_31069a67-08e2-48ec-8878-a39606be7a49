
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { sessions } = await req.json();
    const geminiApiKey = Deno.env.get('GEMINI_API_KEY');
    
    if (!geminiApiKey) {
      throw new Error('GEMINI_API_KEY not configured');
    }

    const upcomingSessions = sessions.filter((s: any) => new Date(s.scheduled_datetime) > new Date());
    const nextSession = upcomingSessions[0];

    let prompt = `As an AI autism therapy assistant, provide helpful preparation tips for upcoming therapy sessions.`;

    if (nextSession) {
      prompt += `

Next session details:
- Type: ${nextSession.session_type}
- Date: ${new Date(nextSession.scheduled_datetime).toLocaleDateString()}
- Duration: ${nextSession.duration_minutes} minutes
- Virtual: ${nextSession.is_virtual ? 'Yes' : 'No'}
${nextSession.focus_areas ? `- Focus Areas: ${nextSession.focus_areas}` : ''}

Provide practical preparation tips (2-3 sentences) to help the patient get ready for their session.`;
    } else {
      prompt += `

The patient has ${sessions.length} total sessions. Provide general tips (2-3 sentences) for preparing for therapy sessions and making the most of them.`;
    }

    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${geminiApiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: prompt
          }]
        }]
      }),
    });

    const data = await response.json();
    const preparation = data.candidates?.[0]?.content?.parts?.[0]?.text || "Prepare for your sessions by getting plenty of rest and thinking about what you'd like to discuss with your therapist.";

    return new Response(JSON.stringify({ preparation }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Error:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
