import { useState, useEffect, useRef } from "react";
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Target, ClipboardList, TrendingUp, Calendar, Star, User, MessageSquare, Gamepad2, Sparkles, Video } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/hooks/useAuth";
import { Skeleton } from "@/components/ui/skeleton";
import { LeezaAIChat } from "@/components/ai/LeezaAIChat";
import { useTranslation } from "react-i18next";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { Carousel, CarouselContent, CarouselItem } from "@/components/ui/carousel"; // add carousel import
import { PatientDashboardCarousel } from "./PatientDashboardCarousel";

interface DashboardStats {
  totalGoals: number;
  completedGoals: number;
  totalActivities: number;
  completedTodayActivities: number;
  totalMilestones: number;
  upcomingSessions: number;
}

interface TherapistInfo {
  first_name: string;
  last_name: string;
  id?: string;
}

interface PatientInfo {
  autism_level: string;
  assigned_therapist_id: string | null;
  id?: string;
}

export function PatientDashboard() {
  const navigate = useNavigate();
  const { profile } = useAuth();
  const { t } = useTranslation();
  const [stats, setStats] = useState<DashboardStats>({
    totalGoals: 0,
    completedGoals: 0,
    totalActivities: 0,
    completedTodayActivities: 0,
    totalMilestones: 0,
    upcomingSessions: 0,
  });
  const [loading, setLoading] = useState(true);
  const [therapistInfo, setTherapistInfo] = useState<TherapistInfo | null>(null);
  const [patientInfo, setPatientInfo] = useState<PatientInfo | null>(null);
  const [showLeezaAI, setShowLeezaAI] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // List for Autism Level dropdown (label, value)
  const autismLevelOptions = [
    { value: "1", label: "Level 1" },
    { value: "2", label: "Level 2" },
    { value: "3", label: "Level 3" },
  ];

  useEffect(() => {
    if (profile?.id) {
      fetchDashboardStats();
    }
  }, [profile]);

  const fetchDashboardStats = async () => {
    setError(null); // reset error before fetch
    console.log("🏠 Starting dashboard stats fetch for user:", profile?.id);
    
    try {
      // Get patient record with therapist assignment
      console.log("📋 Fetching patient data...");
      let { data: patientData, error: patientError } = await supabase
        .from("patients")
        .select("id, assigned_therapist_id, autism_level")
        .eq("user_id", profile?.id)
        .maybeSingle();

      console.log("👤 Dashboard patient query result:", { patientData, patientError });

      if (patientError) {
        setError(`Error fetching patient: ${patientError.message}`);
        setLoading(false);
        return;
      }

      if (!patientData) {
        console.log("🆕 Creating new patient record for user:", profile?.id);
        const { data: newPatient, error: createError } = await supabase
          .from("patients")
          .insert({
            user_id: profile?.id,
            guardian_name: `${profile?.first_name || "Patient"} ${profile?.last_name || "User"}`,
            date_of_birth: new Date().toISOString().split('T')[0],
            autism_level: "1" as const
          })
          .select("id, assigned_therapist_id, autism_level")
          .single();

        console.log("🆕 New patient creation result:", { newPatient, createError });

        if (createError) {
          setError(`Error creating patient: ${createError.message}`);
          setLoading(false);
          return;
        }
        patientData = newPatient;
      }

      setPatientInfo({ 
        id: patientData.id,
        autism_level: patientData.autism_level,
        assigned_therapist_id: patientData.assigned_therapist_id 
      });

      console.log("📊 Patient info set:", { 
        autism_level: patientData.autism_level,
        assigned_therapist_id: patientData.assigned_therapist_id 
      });

      // Fetch therapist info if assigned
      if (patientData.assigned_therapist_id) {
        console.log("🩺 Fetching therapist info for ID:", patientData.assigned_therapist_id);
        
        const { data: therapistData, error: therapistError } = await supabase
          .from("profiles")
          .select("id, first_name, last_name")
          .eq("id", patientData.assigned_therapist_id)
          .eq("role", "therapist")
          .maybeSingle();

        console.log("🩺 Dashboard therapist query result:", { therapistData, therapistError });

        if (therapistError) {
          setError(`Error fetching therapist: ${therapistError.message}`);
          setTherapistInfo(null);
        } else if (!therapistData) {
          console.log("⚠️ No therapist data returned in dashboard");
          setTherapistInfo(null);
        } else {
          console.log("✅ Therapist data found in dashboard:", therapistData);
          setTherapistInfo(therapistData);
        }
      } else {
        console.log("⚠️ No therapist assigned to patient in dashboard");
        setTherapistInfo(null);
      }

      // Fetch goals stats
      const { data: goalsData } = await supabase
        .from("therapy_goals")
        .select("status")
        .eq("patient_id", patientData.id);

      // Fetch activities stats
      const { data: activitiesData } = await supabase
        .from("daily_activities")
        .select("id")
        .eq("patient_id", patientData.id)
        .eq("is_active", true);

      // Fetch today's completed activities
      const today = new Date().toISOString().split('T')[0];
      const { data: completionsData } = await supabase
        .from("activity_completions")
        .select("id")
        .eq("patient_id", patientData.id)
        .eq("completion_date", today)
        .eq("completed", true);

      // Fetch milestones stats
      const { data: milestonesData } = await supabase
        .from("milestones")
        .select("status")
        .eq("patient_id", patientData.id);

      // Fetch upcoming sessions
      const { data: sessionsData } = await supabase
        .from("therapy_sessions")
        .select("id")
        .eq("patient_id", patientData.id)
        .eq("status", "scheduled")
        .gte("scheduled_datetime", new Date().toISOString());

      setStats({
        totalGoals: goalsData?.length || 0,
        completedGoals: goalsData?.filter(g => g.status === 'completed').length || 0,
        totalActivities: activitiesData?.length || 0,
        completedTodayActivities: completionsData?.length || 0,
        totalMilestones: milestonesData?.filter(m => m.status === 'completed').length || 0,
        upcomingSessions: sessionsData?.length || 0,
      });

      console.log("📈 Final dashboard stats:", {
        totalGoals: goalsData?.length || 0,
        totalActivities: activitiesData?.length || 0,
        upcomingSessions: sessionsData?.length || 0,
        hasAssignedTherapist: !!patientData.assigned_therapist_id,
        therapistInfo
      });
    } catch (error: any) {
      setError(error?.message || "Unknown dashboard error");
      console.error("💥 Unexpected error fetching dashboard stats:", error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-4xl mx-auto space-y-6">
          <Skeleton className="h-32 w-full rounded-xl" />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {[1, 2, 3, 4].map((i) => (
              <Skeleton key={i} className="h-40 w-full rounded-xl" />
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Add error display
  if (error) {
    return (
      <div className="min-h-screen flex flex-col justify-center items-center bg-gray-50 p-6">
        <div className="bg-red-100 border border-red-400 text-red-800 px-4 py-4 rounded-lg max-w-lg w-full">
          <div className="text-lg font-bold mb-2">Error loading dashboard</div>
          <div>{error}</div>
        </div>
      </div>
    );
  }

  // Fallback for missing required data
  if (!profile) {
    return (
      <div className="min-h-screen flex flex-col justify-center items-center bg-gray-50 p-6">
        <div className="bg-yellow-50 text-yellow-800 px-4 py-2 rounded-lg">Missing user profile data. Please try refreshing or contact support.</div>
      </div>
    );
  }

  console.log("🖼️ Rendering dashboard with therapist info:", therapistInfo);
  console.log("🖼️ Rendering dashboard with patient info:", patientInfo);

  try {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Enhanced Header */}
        <div className="bg-white shadow-sm border-b">
          <div className="max-w-4xl mx-auto px-4 md:px-6 py-6 md:py-8">
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-14 h-14 md:w-20 md:h-20 bg-app-purple/10 rounded-full mb-3 md:mb-4">
                <User className="h-7 w-7 md:h-10 md:w-10 text-app-purple" />
              </div>
              <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-1 md:mb-2">
                {t('dashboard.welcomeBack', { name: profile?.first_name || t('patient.defaultName', 'Patient') })}
              </h1>
              <p className="text-gray-600 text-base md:text-lg">
                {therapistInfo 
                  ? t('patient.yourTherapist', { name: `${therapistInfo.first_name} ${therapistInfo.last_name}` })
                  : t('patient.waitingTherapistAssignment')
                }
              </p>
              <div className="flex justify-center mt-3 md:mt-4">
                {!patientInfo?.assigned_therapist_id ? (
                  <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200 px-3 md:px-4 py-1 md:py-2 text-xs md:text-sm">
                    {t('patient.pendingTherapistAssignment')}
                  </Badge>
                ) : (
                  <Badge variant="outline" className="bg-emerald-50 text-emerald-700 border-emerald-200 px-3 md:px-4 py-1 md:py-2 text-xs md:text-sm">
                    {t('patient.therapistAssigned')}
                  </Badge>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-4xl mx-auto px-2 md:px-6 py-4 md:py-8 space-y-6 md:space-y-8">

          {/* --- REFACTORED: Carousel with Autism Level, Completed Goals, Milestones --- */}
          <PatientDashboardCarousel
            autismLevel={patientInfo?.autism_level}
            completedGoals={stats.completedGoals}
            totalGoals={stats.totalGoals}
            totalMilestones={stats.totalMilestones}
          />

          {/* Leeza AI Assistant Card */}
          <Card 
            className="bg-app-purple text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-[1.02] !rounded-2xl"
            onClick={() => setShowLeezaAI(true)}
          >
            <CardContent className="flex flex-col sm:flex-row items-center justify-between p-4 md:p-8">
              <div className="flex items-center space-x-3 md:space-x-6">
                <div className="w-10 h-10 md:w-20 md:h-20 bg-white/20 rounded-xl flex items-center justify-center backdrop-blur-sm">
                  <Sparkles className="h-5 w-5 md:h-10 md:w-10 text-white" />
                </div>
                <div>
                  <h3 className="text-lg md:text-2xl font-bold text-white mb-1">{t('patient.leezaAIAssistant')}</h3>
                  {/* Removed t('patient.aiCompanionDescription') to reduce unnecessary info */}
                </div>
              </div>
              <div className="flex flex-col items-end mt-3 sm:mt-0 min-w-max">
                {/* "Available 24/7" and "AI powered" badge removed to declutter for parents */}
              </div>
            </CardContent>
          </Card>

          {/* Main Navigation Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-6">
            {/* Therapy Goals */}
            <Card 
              className="bg-white border-0 shadow-sm hover:shadow-lg transition-all duration-300 cursor-pointer group"
              onClick={() => navigate("/patient/goals")}
            >
              <CardContent className="p-4 md:p-8">
                <div className="flex items-start justify-between mb-3 md:mb-6">
                  <div className="w-10 h-10 md:w-16 md:h-16 bg-app-yellow/10 rounded-2xl flex items-center justify-center group-hover:bg-app-yellow/20 transition-colors">
                    <Target className="h-5 w-5 md:h-8 md:w-8 text-app-yellow" />
                  </div>
                  {/* Just show goal count, remove coded label */}
                  <Badge variant="outline" className="bg-gray-50 text-gray-600 border-gray-200 text-xs md:text-base px-2 md:px-4 py-1 md:py-2">
                    {stats.totalGoals}
                  </Badge>
                </div>
                <h3 className="text-base md:text-xl font-bold text-gray-900 mb-1 md:mb-2">{t('patient.therapyGoals')}</h3>
                {/* Removed extra description text */}
              </CardContent>
            </Card>

            {/* Daily Activities */}
            <Card 
              className="bg-white border-0 shadow-sm hover:shadow-lg transition-all duration-300 cursor-pointer group"
              onClick={() => navigate("/patient/activities")}
            >
              <CardContent className="p-4 md:p-8">
                <div className="flex items-start justify-between mb-3 md:mb-6">
                  <div className="w-10 h-10 md:w-16 md:h-16 bg-app-orange/10 rounded-2xl flex items-center justify-center group-hover:bg-app-orange/20 transition-colors">
                    <ClipboardList className="h-5 w-5 md:h-8 md:w-8 text-app-orange" />
                  </div>
                  {/* Show just activities counts, no coded text */}
                  <Badge variant="outline" className="bg-gray-50 text-gray-600 border-gray-200 text-xs md:text-base px-2 md:px-4 py-1 md:py-2">
                    {stats.completedTodayActivities}/{stats.totalActivities}
                  </Badge>
                </div>
                <h3 className="text-base md:text-xl font-bold text-gray-900 mb-1 md:mb-2">{t('patient.myActivities')}</h3>
              </CardContent>
            </Card>

            {/* Development Milestones */}
            <Card 
              className="bg-white border-0 shadow-sm hover:shadow-lg transition-all duration-300 cursor-pointer group"
              onClick={() => navigate("/patient/milestones")}
            >
              <CardContent className="p-4 md:p-8">
                <div className="flex items-start justify-between mb-3 md:mb-6">
                  <div className="w-10 h-10 md:w-16 md:h-16 bg-app-green/10 rounded-2xl flex items-center justify-center group-hover:bg-app-green/20 transition-colors">
                    <TrendingUp className="h-5 w-5 md:h-8 md:w-8 text-app-green" />
                  </div>
                  <div className="flex items-center space-x-1">
                    {[...Array(Math.min(3, stats.totalMilestones))].map((_, i) => (
                      <Star key={i} className="h-3 w-3 md:h-4 md:w-4 text-app-yellow fill-current" />
                    ))}
                  </div>
                </div>
                <h3 className="text-base md:text-xl font-bold text-gray-900 mb-1 md:mb-2">{t('patient.myMilestones')}</h3>
              </CardContent>
            </Card>

            {/* Scheduled Sessions */}
            <Card 
              className="bg-white border-0 shadow-sm hover:shadow-lg transition-all duration-300 cursor-pointer group"
              onClick={() => navigate("/patient/sessions")}
            >
              <CardContent className="p-4 md:p-8">
                <div className="flex items-start justify-between mb-3 md:mb-6">
                  <div className="w-10 h-10 md:w-16 md:h-16 bg-blue-50 rounded-2xl flex items-center justify-center group-hover:bg-blue-100 transition-colors">
                    <Calendar className="h-5 w-5 md:h-8 md:w-8 text-blue-600" />
                  </div>
                  {/* Just show count, remove coded key */}
                  <Badge variant="outline" className="bg-gray-50 text-gray-600 border-gray-200 text-xs md:text-base px-2 md:px-4 py-1 md:py-2">
                    {stats.upcomingSessions}
                  </Badge>
                </div>
                <h3 className="text-base md:text-xl font-bold text-gray-900 mb-1 md:mb-2">{t('patient.sessions')}</h3>
              </CardContent>
            </Card>
          </div>

          {/* Skills Game - Special Card */}
          <Card 
            className="bg-indigo-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-[1.02] !rounded-2xl"
            onClick={() => navigate("/patient/game")}
          >
            <CardContent className="flex flex-col sm:flex-row items-center justify-between p-4 md:p-8">
              <div className="flex items-center space-x-3 md:space-x-6">
                <div className="w-10 h-10 md:w-20 md:h-20 bg-white/20 rounded-xl flex items-center justify-center backdrop-blur-sm">
                  <Gamepad2 className="h-6 w-6 md:h-10 md:w-10 text-white" />
                </div>
                <div>
                  <h3 className="text-lg md:text-2xl font-bold text-white mb-1">{t('patient.game')}</h3>
                  {/* Removed t('patient.gameDescription') for simplicity */}
                </div>
              </div>
              <div className="flex flex-col items-end mt-3 sm:mt-0 min-w-max">
                {/* "New" badge (if needed, or remove for minimalism) */}
                {/* <Badge variant="secondary" className="bg-white/20 text-white border-white/30 mb-1 text-xs md:text-sm">
                  {t('patient.new')}
                </Badge> */}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Leeza AI Chat Modal */}
        <LeezaAIChat isOpen={showLeezaAI} onClose={() => setShowLeezaAI(false)} />
      </div>
    );
  } catch (err: any) {
    // Show a hard error fallback UI for any unexpected error
    return (
      <div className="min-h-screen flex flex-col justify-center items-center bg-gray-50 p-6">
        <div className="bg-red-100 border border-red-400 text-red-800 px-4 py-4 rounded-lg max-w-lg w-full">
          <div className="text-lg font-bold mb-2">Unexpected error occurred in dashboard</div>
          <div>{err?.message || String(err)}</div>
        </div>
      </div>
    );
  }
}
