
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import { GlobalVoiceCommand } from './useGlobalVoiceAgent';

export const useVoiceEventHandler = () => {
  const navigate = useNavigate();

  useEffect(() => {
    const handleVoiceEvent = (event: CustomEvent<GlobalVoiceCommand>) => {
      const command = event.detail;
      console.log('🎯 Voice event received:', command);

      try {
        switch (command.action) {
          case 'view_patients':
            navigate('/therapist/patients');
            toast.success('Navigating to patients list');
            break;
            
          case 'view_goals':
            navigate('/therapist/goals');
            toast.success('Navigating to goals management');
            break;
            
          case 'view_activities':
            navigate('/therapist/activities');
            toast.success('Navigating to activities');
            break;
            
          case 'view_sessions':
            navigate('/therapist/sessions');
            toast.success('Navigating to sessions');
            break;
            
          case 'navigate':
            if (command.navigation) {
              navigate(command.navigation);
              toast.success(`Navigating to ${command.navigation}`);
            } else {
              toast.error('Navigation path not specified');
            }
            break;
            
          case 'view_patient':
            if (command.parameters?.patient_name) {
              // Enhanced patient search logic
              navigate('/therapist/patients');
              toast.info(`Looking for patient: ${command.parameters.patient_name}`);
              
              // Context-aware patient highlighting
              setTimeout(() => {
                const patientElements = document.querySelectorAll('[data-patient-name]');
                patientElements.forEach(el => {
                  const name = el.getAttribute('data-patient-name')?.toLowerCase();
                  if (name?.includes(command.parameters.patient_name.toLowerCase())) {
                    el.scrollIntoView({ behavior: 'smooth' });
                    el.classList.add('ring-2', 'ring-blue-500');
                  }
                });
              }, 1000);
            } else {
              navigate('/therapist/patients');
              toast.warning('Patient name not specified. Showing all patients.');
            }
            break;
            
          case 'create_goal':
            navigate('/therapist/goals');
            if (command.parameters?.patient_name) {
              toast.info(`Ready to create goal for: ${command.parameters.patient_name}`);
              if (command.parameters?.goal_title) {
                toast.info(`Goal topic: ${command.parameters.goal_title}`);
              }
            } else {
              toast.info('Ready to create new goal');
            }
            break;
            
          case 'schedule_session':
            navigate('/therapist/sessions');
            if (command.parameters?.datetime) {
              toast.info(`Schedule session for: ${command.parameters.datetime}`);
            }
            if (command.parameters?.patient_name) {
              toast.info(`Patient: ${command.parameters.patient_name}`);
            }
            if (command.parameters?.session_type) {
              toast.info(`Session type: ${command.parameters.session_type}`);
            }
            break;
            
          case 'update_progress':
            if (command.parameters?.progress_percentage) {
              const percentage = command.parameters.progress_percentage;
              if (command.parameters?.patient_name) {
                toast.success(`Updated ${command.parameters.patient_name}'s progress to ${percentage}%`);
              } else {
                toast.info(`Ready to update progress to ${percentage}%`);
              }
            } else {
              toast.warning('Progress percentage not specified');
            }
            break;
            
          case 'generate_report':
            if (command.parameters?.patient_name) {
              toast.info(`Generating report for: ${command.parameters.patient_name}`);
              // Simulate report generation
              setTimeout(() => {
                toast.success(`Report generated for ${command.parameters.patient_name}`);
              }, 2000);
            } else {
              toast.warning('Patient name required for report generation');
            }
            break;
            
          case 'help':
            toast.info('Voice commands: view patients, create goals, schedule sessions, update progress, generate reports', {
              duration: 5000
            });
            break;
            
          default:
            console.log('Unknown voice command:', command.action);
            toast.warning(`Unknown command: ${command.action}`);
        }

        // Show confidence indicator
        if (command.confidence === 'low') {
          toast.warning('Low confidence in command interpretation. Please verify the action is correct.');
        }

        // Handle clarification requests
        if (command.clarification_needed) {
          toast.info(command.clarification_needed, { duration: 6000 });
        }

      } catch (error) {
        console.error('Error handling voice command:', error);
        toast.error('Failed to execute voice command. Please try again.');
      }
    };

    // Register all possible voice event listeners
    const voiceEvents = [
      'voice-view-patients',
      'voice-view-goals', 
      'voice-view-activities',
      'voice-view-sessions',
      'voice-navigate',
      'voice-view-patient',
      'voice-create-goal',
      'voice-schedule-session',
      'voice-update-progress',
      'voice-generate-report',
      'voice-help'
    ];

    voiceEvents.forEach(eventName => {
      window.addEventListener(eventName, handleVoiceEvent as EventListener);
    });

    return () => {
      voiceEvents.forEach(eventName => {
        window.removeEventListener(eventName, handleVoiceEvent as EventListener);
      });
    };
  }, [navigate]);
};
