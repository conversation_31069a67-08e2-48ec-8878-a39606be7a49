
-- 1. <PERSON><PERSON> simplified daily_entries table (tracks daily parent/therapist input)
CREATE TABLE public.daily_entries (
  id uuid PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
  patient_id uuid NOT NULL,
  therapist_id uuid NULL,
  entry_date date NOT NULL DEFAULT CURRENT_DATE,
  activity_ids uuid[] NULL,
  session_id uuid NULL,
  completed_activities integer NULL,
  total_activities integer NULL,
  progress_note text NULL,
  observation text NULL,
  created_by_role text NOT NULL, -- 'parent' or 'therapist'
  created_at timestamp with time zone NOT NULL DEFAULT now()
);

-- 2. Add minimal indexes for performance
CREATE INDEX ON public.daily_entries (patient_id, entry_date);

-- 3. Add simple status column to therapy_goals
ALTER TABLE public.therapy_goals
  ADD COLUMN IF NOT EXISTS simple_status text NOT NULL DEFAULT 'active'; -- not_started, active, achieved, paused

-- 4. (OPTIONAL) Remove/ignore redundant "activity_completions" and "observations" tables if desired in future.

-- 5. Enable RLS and allow only authorized users (parents/therapists) to access their data
ALTER TABLE public.daily_entries ENABLE ROW LEVEL SECURITY;

-- Policy: allow therapist who is assigned to the patient
CREATE POLICY "therapists can access patient daily entries"
ON public.daily_entries
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM patients
    WHERE id = patient_id
    AND assigned_therapist_id = auth.uid()
  )
);

-- Policy: allow parent/guardian (patient owner)
CREATE POLICY "parents can access their child daily entries"
ON public.daily_entries
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM patients
    WHERE id = patient_id
    AND user_id = auth.uid()
  )
);

-- Insert policies for INSERT/UPDATE/DELETE as above as needed:
CREATE POLICY "therapists can insert daily entry"
  ON public.daily_entries FOR INSERT
  WITH CHECK (
    EXISTS (SELECT 1 FROM patients WHERE id = patient_id AND assigned_therapist_id = auth.uid())
  );

CREATE POLICY "parents can insert their own child daily entry"
  ON public.daily_entries FOR INSERT
  WITH CHECK (
    EXISTS (SELECT 1 FROM patients WHERE id = patient_id AND user_id = auth.uid())
  );

-- (You can later prune unused tables for additional simplicity)

