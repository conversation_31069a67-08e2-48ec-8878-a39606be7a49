
import { Card, CardContent } from "@/components/ui/card";

export function PatientLoadingState() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-md mx-auto px-6 py-8">
        <div className="space-y-6">
          {/* Header skeleton */}
          <div className="flex items-center space-x-4">
            <div className="w-8 h-8 bg-gray-200 rounded animate-pulse"></div>
            <div className="w-32 h-6 bg-gray-200 rounded animate-pulse"></div>
          </div>
          
          {/* Date slider skeleton */}
          <div className="flex space-x-2 overflow-hidden">
            {[...Array(7)].map((_, i) => (
              <div key={i} className="w-12 h-16 bg-gray-200 rounded-lg animate-pulse flex-shrink-0"></div>
            ))}
          </div>
          
          {/* Session details skeleton */}
          <Card className="rounded-3xl">
            <CardContent className="p-6">
              <div className="space-y-3">
                <div className="w-24 h-4 bg-gray-200 rounded animate-pulse"></div>
                <div className="w-full h-6 bg-gray-200 rounded animate-pulse"></div>
                <div className="w-3/4 h-4 bg-gray-200 rounded animate-pulse"></div>
              </div>
            </CardContent>
          </Card>
          
          {/* Goals skeleton */}
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="rounded-3xl">
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div className="flex justify-between items-start">
                    <div className="space-y-2">
                      <div className="w-40 h-5 bg-gray-200 rounded animate-pulse"></div>
                      <div className="flex space-x-2">
                        <div className="w-16 h-6 bg-gray-200 rounded-full animate-pulse"></div>
                        <div className="w-16 h-6 bg-gray-200 rounded-full animate-pulse"></div>
                      </div>
                    </div>
                    <div className="w-12 h-8 bg-gray-200 rounded animate-pulse"></div>
                  </div>
                  <div className="w-full h-4 bg-gray-200 rounded animate-pulse"></div>
                  <div className="w-full h-2 bg-gray-200 rounded-full animate-pulse"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
