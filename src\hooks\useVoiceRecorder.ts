
import { useRef, useState } from "react";

type RecorderState = "idle" | "recording" | "stopped" | "error";

export function useVoiceRecorder() {
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [state, setState] = useState<RecorderState>("idle");
  const [error, setError] = useState<string | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const recorderRef = useRef<MediaRecorder | null>(null);
  const chunks = useRef<Blob[]>([]);

  const startRecording = async () => {
    setError(null);
    try {
      if (!navigator.mediaDevices?.getUserMedia) {
        setError("Media devices not supported");
        return;
      }
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      streamRef.current = stream;
      const recorder = new MediaRecorder(stream, { mimeType: "audio/webm" });
      recorderRef.current = recorder;
      chunks.current = [];
      recorder.ondataavailable = (e) => {
        if (e.data.size > 0) chunks.current.push(e.data);
      };
      recorder.onstop = () => {
        const blob = new Blob(chunks.current, { type: "audio/webm" });
        setAudioUrl(URL.createObjectURL(blob));
        setState("stopped");
      };
      recorder.start();
      setState("recording");
    } catch (err: any) {
      setError(err.message || "Failed to record");
      setState("error");
    }
  };

  const stopRecording = () => {
    recorderRef.current?.stop();
    streamRef.current?.getTracks().forEach((track) => track.stop());
    setState("stopped");
  };

  const reset = () => {
    setAudioUrl(null);
    setState("idle");
    setError(null);
    chunks.current = [];
  };

  return {
    audioUrl,
    state,
    error,
    startRecording,
    stopRecording,
    reset,
  };
}
