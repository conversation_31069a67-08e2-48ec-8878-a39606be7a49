import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Calendar, Clock, Video, MapPin, User, <PERSON>rkles, ArrowLeft } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { Layout } from "@/components/Layout";
import { Skeleton } from "@/components/ui/skeleton";
import { ObservationsCard } from "@/components/ui/observations-card";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";

interface TherapySession {
  id: string;
  scheduled_datetime: string;
  duration_minutes: number;
  session_type: string;
  status: string;
  is_virtual: boolean;
  meeting_link: string;
  session_notes: string;
  observations: string;
  focus_areas: string;
  therapist: {
    first_name: string;
    last_name: string;
  };
}

export default function Sessions() {
  const [sessions, setSessions] = useState<TherapySession[]>([]);
  const [loading, setLoading] = useState(true);
  const [aiPreparation, setAiPreparation] = useState<string>("");
  const { profile } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const { t } = useTranslation();

  useEffect(() => {
    if (profile?.id) {
      fetchPatientSessions();
    }
  }, [profile]);

  const fetchPatientSessions = async () => {
    try {
      console.log("Starting to fetch sessions for user:", profile?.id);
      
      let { data: patientData, error: patientError } = await supabase
        .from("patients")
        .select("id")
        .eq("user_id", profile?.id)
        .maybeSingle();

      console.log("Patient data:", patientData, "Error:", patientError);

      if (patientError) {
        console.error("Error fetching patient:", patientError);
        setSessions([]);
        setLoading(false);
        return;
      }

      if (!patientData) {
        console.log("No patient record found - showing empty state");
        setSessions([]);
        setLoading(false);
        return;
      }

      const { data: sessionsData, error: sessionsError } = await supabase
        .from("therapy_sessions")
        .select(`
          *,
          therapist:profiles!therapist_id(first_name, last_name)
        `)
        .eq("patient_id", patientData.id)
        .order("scheduled_datetime", { ascending: true });

      console.log("Sessions data:", sessionsData, "Error:", sessionsError);

      if (sessionsError) {
        console.error("Error fetching sessions:", sessionsError);
        toast({
          title: "Error",
          description: "Failed to load your sessions. Please try again.",
          variant: "destructive",
        });
        setSessions([]);
      } else {
        setSessions(sessionsData || []);
        
        if (sessionsData && sessionsData.length > 0) {
          generateAIPreparation(sessionsData);
        }
      }
    } catch (error: any) {
      console.error("Unexpected error:", error);
      toast({
        title: "Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
      setSessions([]);
    } finally {
      setLoading(false);
    }
  };

  const generateAIPreparation = async (sessionsData: TherapySession[]) => {
    try {
      const response = await supabase.functions.invoke('generate-session-preparation', {
        body: { sessions: sessionsData }
      });

      if (response.data?.preparation) {
        setAiPreparation(response.data.preparation);
      }
    } catch (error) {
      console.log("AI preparation unavailable:", error);
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="min-h-screen bg-gray-50 p-4">
          <div className="max-w-md mx-auto space-y-6">
            <Skeleton className="h-20 w-full rounded-2xl" />
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <Skeleton key={i} className="h-32 w-full rounded-2xl" />
              ))}
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white px-6 py-6 flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate(-1)}
            className="p-2"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-xl font-bold text-gray-900">{t('patient.sessions')}</h1>
        </div>

        <div className="max-w-md mx-auto px-6 space-y-6">
          {/* AI Session Preparation Card */}
          {aiPreparation && (
            <Card className="app-gradient-purple text-white border-0 shadow-lg rounded-3xl">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Sparkles className="h-6 w-6" />
                  <span>{t('patient.sessionPreparation')}</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-white/90">{aiPreparation}</p>
              </CardContent>
            </Card>
          )}

          {/* Sessions List */}
          <div className="space-y-6">
            {sessions.length === 0 ? (
              <Card className="app-card shadow-lg rounded-3xl">
                <CardContent className="p-12 text-center">
                  <Calendar className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">{t('patient.noSessions')}</h3>
                  <p className="text-gray-600">{t('patient.noSessionsDescription')}</p>
                </CardContent>
              </Card>
            ) : (
              sessions.map((session) => (
                <div key={session.id} className="space-y-4">
                  <Card className="app-card app-card-hover shadow-lg rounded-3xl">
                    <CardHeader>
                      <div className="flex justify-between items-start">
                        <div className="space-y-3">
                          <CardTitle className="text-lg text-gray-900">
                            {t(`therapyTypes.${session.session_type}`)} {t('patient.session')}
                          </CardTitle>
                          <div className="flex items-center space-x-4 flex-wrap">
                            <div className="flex items-center space-x-2">
                              <User className="h-4 w-4 text-gray-500" />
                              <span className="text-sm text-gray-600">
                                {session.therapist?.first_name} {session.therapist?.last_name}
                              </span>
                            </div>
                            <Badge className="bg-app-purple text-white">
                              {t(`status.${session.status}`)}
                            </Badge>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="flex items-center space-x-2 text-sm text-gray-600 mb-1">
                            <Calendar className="h-4 w-4" />
                            <span>{new Date(session.scheduled_datetime).toLocaleDateString()}</span>
                          </div>
                          <div className="flex items-center space-x-2 text-sm text-gray-600">
                            <Clock className="h-4 w-4" />
                            <span>{new Date(session.scheduled_datetime).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</span>
                          </div>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {session.focus_areas && (
                        <p className="text-gray-600">
                          <strong>{t('patient.focusAreas')}:</strong> {session.focus_areas}
                        </p>
                      )}
                      
                      <div className="flex items-center justify-between flex-wrap gap-4">
                        <div className="flex items-center space-x-4">
                          <div className="flex items-center space-x-2">
                            {session.is_virtual ? (
                              <Video className="h-4 w-4 text-app-green" />
                            ) : (
                              <MapPin className="h-4 w-4 text-app-purple" />
                            )}
                            <span className="text-sm text-gray-600">
                              {session.is_virtual ? t('patient.virtual') : t('patient.inPerson')}
                            </span>
                          </div>
                          <span className="text-sm text-gray-600">
                            {session.duration_minutes} {t('patient.minutes')}
                          </span>
                        </div>
                        
                        {session.is_virtual && session.meeting_link && (
                          <Button 
                            size="sm"
                            className="app-gradient-purple text-white border-0 rounded-full"
                            onClick={() => window.open(session.meeting_link, '_blank')}
                          >
                            <Video className="h-4 w-4 mr-2" />
                            {t('patient.joinSession')}
                          </Button>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                  
                  {/* Session Observations */}
                  {session.observations && (
                    <ObservationsCard
                      observations={[{
                        id: session.id,
                        observation_text: session.observations,
                        observation_date: new Date(session.scheduled_datetime).toISOString().split('T')[0],
                        created_at: session.scheduled_datetime,
                      }]}
                      isTherapist={false}
                      className="ml-4 rounded-3xl"
                    />
                  )}
                </div>
              ))
            )}
          </div>

          {/* Bottom spacing */}
          <div className="h-8"></div>
        </div>
      </div>
    </Layout>
  );
}
