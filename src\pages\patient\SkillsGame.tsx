
import { useState, useEffect, useCallback } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowLeft, Play, Pause, RotateCcw, Trophy, Star } from "lucide-react";
import { Layout } from "@/components/Layout";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";

interface GameState {
  level: number;
  score: number;
  timeLeft: number;
  isPlaying: boolean;
  gameMode: 'motor' | 'cognitive' | 'speech';
}

interface Target {
  id: number;
  x: number;
  y: number;
  color: string;
  shape: string;
  isActive: boolean;
}

const COLORS = ['red', 'blue', 'green', 'yellow', 'purple', 'orange'];
const SHAPES = ['circle', 'square', 'triangle', 'star'];
const SPEECH_WORDS = ['cat', 'dog', 'sun', 'tree', 'car', 'ball', 'book', 'fish'];

export default function SkillsGame() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [gameState, setGameState] = useState<GameState>({
    level: 1,
    score: 0,
    timeLeft: 60,
    isPlaying: false,
    gameMode: 'motor'
  });
  const [targets, setTargets] = useState<Target[]>([]);
  const [currentWord, setCurrentWord] = useState('');
  const [speechRecognition, setSpeechRecognition] = useState<any>(null);

  // Initialize speech recognition
  useEffect(() => {
    if (typeof window !== 'undefined' && ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)) {
      const SpeechRecognitionClass = (window as any).webkitSpeechRecognition || (window as any).SpeechRecognition;
      const recognition = new SpeechRecognitionClass();
      recognition.continuous = false;
      recognition.interimResults = false;
      recognition.lang = 'en-US';
      setSpeechRecognition(recognition);
    }
  }, []);

  const generateTargets = useCallback(() => {
    const newTargets: Target[] = [];
    const targetCount = Math.min(3 + gameState.level, 8);
    
    for (let i = 0; i < targetCount; i++) {
      newTargets.push({
        id: i,
        x: Math.random() * 80 + 10,
        y: Math.random() * 60 + 20,
        color: COLORS[Math.floor(Math.random() * COLORS.length)],
        shape: SHAPES[Math.floor(Math.random() * SHAPES.length)],
        isActive: true
      });
    }
    setTargets(newTargets);
  }, [gameState.level]);

  const generateSpeechWord = useCallback(() => {
    const word = SPEECH_WORDS[Math.floor(Math.random() * SPEECH_WORDS.length)];
    setCurrentWord(word);
  }, []);

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (gameState.isPlaying && gameState.timeLeft > 0) {
      timer = setTimeout(() => {
        setGameState(prev => ({ ...prev, timeLeft: prev.timeLeft - 1 }));
      }, 1000);
    } else if (gameState.timeLeft === 0) {
      setGameState(prev => ({ ...prev, isPlaying: false }));
    }
    return () => clearTimeout(timer);
  }, [gameState.isPlaying, gameState.timeLeft]);

  const startGame = () => {
    setGameState(prev => ({ 
      ...prev, 
      isPlaying: true, 
      timeLeft: 60,
      score: 0 
    }));
    
    if (gameState.gameMode === 'motor' || gameState.gameMode === 'cognitive') {
      generateTargets();
    } else if (gameState.gameMode === 'speech') {
      generateSpeechWord();
    }
  };

  const pauseGame = () => {
    setGameState(prev => ({ ...prev, isPlaying: !prev.isPlaying }));
  };

  const resetGame = () => {
    setGameState({
      level: 1,
      score: 0,
      timeLeft: 60,
      isPlaying: false,
      gameMode: gameState.gameMode
    });
    setTargets([]);
    setCurrentWord('');
  };

  const handleTargetClick = (targetId: number) => {
    if (!gameState.isPlaying) return;
    
    setTargets(prev => prev.map(target => 
      target.id === targetId 
        ? { ...target, isActive: false }
        : target
    ));
    
    setGameState(prev => ({ 
      ...prev, 
      score: prev.score + 10 * prev.level 
    }));

    // Generate new targets after a short delay
    setTimeout(() => {
      if (gameState.gameMode === 'motor' || gameState.gameMode === 'cognitive') {
        generateTargets();
      }
    }, 500);
  };

  const startSpeechRecognition = () => {
    if (!speechRecognition || !gameState.isPlaying) return;
    
    speechRecognition.onresult = (event: any) => {
      const transcript = event.results[0][0].transcript.toLowerCase().trim();
      if (transcript === currentWord.toLowerCase()) {
        setGameState(prev => ({ 
          ...prev, 
          score: prev.score + 20 * prev.level 
        }));
        generateSpeechWord();
      }
    };
    
    speechRecognition.start();
  };

  const getShapeStyle = (shape: string, color: string) => {
    const baseStyle = "w-12 h-12 cursor-pointer transition-all duration-200 hover:scale-110";
    const colorClass = `bg-${color}-500`;
    
    switch (shape) {
      case 'circle':
        return `${baseStyle} ${colorClass} rounded-full`;
      case 'square':
        return `${baseStyle} ${colorClass} rounded-lg`;
      case 'triangle':
        return `${baseStyle} ${colorClass} clip-triangle`;
      case 'star':
        return `${baseStyle} ${colorClass} star-shape`;
      default:
        return `${baseStyle} ${colorClass} rounded-lg`;
    }
  };

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-md mx-auto">
          {/* Header */}
          <div className="bg-white px-6 py-6 flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate(-1)}
              className="p-2"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <h1 className="text-xl font-bold text-gray-900">{t('patient.game')}</h1>
          </div>

          <div className="px-6 space-y-6">
            {/* Game Mode Selection */}
            <Card className="rounded-3xl">
              <CardContent className="p-4">
                <h3 className="text-lg font-semibold mb-3">{t('patient.chooseGameMode')}</h3>
                <div className="grid grid-cols-3 gap-2">
                  {(['motor', 'cognitive', 'speech'] as const).map((mode) => (
                    <Button
                      key={mode}
                      variant={gameState.gameMode === mode ? "default" : "outline"}
                      size="sm"
                      onClick={() => setGameState(prev => ({ ...prev, gameMode: mode }))}
                      className="capitalize"
                    >
                      {t(`categories.${mode}`)}
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Game Stats */}
            <Card className="rounded-3xl">
              <CardContent className="p-4">
                <div className="flex justify-between items-center">
                  <div className="flex items-center space-x-2">
                    <Trophy className="h-5 w-5 text-yellow-500" />
                    <span className="font-semibold">{t('patient.score')}: {gameState.score}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Star className="h-5 w-5 text-blue-500" />
                    <span className="font-semibold">{t('patient.level')}: {gameState.level}</span>
                  </div>
                  <div className="text-lg font-bold text-red-500">
                    {gameState.timeLeft}s
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Game Controls */}
            <div className="flex space-x-3">
              <Button
                onClick={startGame}
                disabled={gameState.isPlaying}
                className="flex-1"
              >
                <Play className="h-4 w-4 mr-2" />
                {t('patient.start')}
              </Button>
              <Button
                onClick={pauseGame}
                variant="outline"
                disabled={!gameState.isPlaying && gameState.timeLeft === 60}
              >
                <Pause className="h-4 w-4" />
              </Button>
              <Button
                onClick={resetGame}
                variant="outline"
              >
                <RotateCcw className="h-4 w-4" />
              </Button>
            </div>

            {/* Game Area */}
            <Card className="rounded-3xl">
              <CardContent className="p-6">
                {gameState.gameMode === 'speech' ? (
                  <div className="text-center space-y-6">
                    <h3 className="text-2xl font-bold">{t('patient.sayTheWord')}:</h3>
                    <div className="text-4xl font-bold text-app-purple">
                      {currentWord.toUpperCase()}
                    </div>
                    <Button
                      onClick={startSpeechRecognition}
                      disabled={!gameState.isPlaying}
                      className="w-full"
                    >
                      🎤 {t('patient.startSpeaking')}
                    </Button>
                  </div>
                ) : (
                  <div className="relative h-80 bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl overflow-hidden">
                    {gameState.gameMode === 'cognitive' && (
                      <div className="absolute top-4 left-4 text-sm font-semibold">
                        {t('patient.tapOnlyColor')} {COLORS[gameState.level % COLORS.length]} {t('patient.shapes')}!
                      </div>
                    )}
                    
                    {targets.map((target) => (
                      target.isActive && (
                        <div
                          key={target.id}
                          className={getShapeStyle(target.shape, target.color)}
                          style={{
                            position: 'absolute',
                            left: `${target.x}%`,
                            top: `${target.y}%`,
                          }}
                          onClick={() => {
                            if (gameState.gameMode === 'motor') {
                              handleTargetClick(target.id);
                            } else if (gameState.gameMode === 'cognitive') {
                              // Only allow clicking specific color in cognitive mode
                              if (target.color === COLORS[gameState.level % COLORS.length]) {
                                handleTargetClick(target.id);
                              }
                            }
                          }}
                        />
                      )
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Instructions */}
            <Card className="rounded-3xl">
              <CardContent className="p-4">
                <h4 className="font-semibold mb-2">{t('patient.howToPlay')}:</h4>
                <div className="text-sm text-gray-600 space-y-1">
                  {gameState.gameMode === 'motor' && (
                    <p>• {t('patient.motorInstructions')}</p>
                  )}
                  {gameState.gameMode === 'cognitive' && (
                    <p>• {t('patient.cognitiveInstructions')}</p>
                  )}
                  {gameState.gameMode === 'speech' && (
                    <p>• {t('patient.speechInstructions')}</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </Layout>
  );
}
