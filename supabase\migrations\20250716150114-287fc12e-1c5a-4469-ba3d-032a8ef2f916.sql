
-- Fix the handle_new_user function to properly handle all authentication flows
CREATE OR REPLACE FUNCTION public.handle_new_user()
R<PERSON><PERSON><PERSON> trigger
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Insert into profiles table with better error handling
  INSERT INTO public.profiles (id, email, first_name, last_name, role, google_id)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data ->> 'first_name', ''),
    COALESCE(NEW.raw_user_meta_data ->> 'last_name', ''),
    COALESCE(NEW.raw_user_meta_data ->> 'role', 'patient'),
    COALESCE(
      NEW.raw_user_meta_data ->> 'provider_id', 
      NEW.raw_user_meta_data ->> 'sub',
      CASE WHEN NEW.app_metadata ->> 'provider' = 'google' THEN NEW.app_metadata ->> 'provider_id' END
    )
  );

  -- Insert into auth_flows table with better provider detection
  INSERT INTO public.auth_flows (user_id, flow_type, google_auth, profile_completed)
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data ->> 'role', 'patient'),
    CASE 
      WHEN NEW.app_metadata ->> 'provider' = 'google' THEN TRUE
      WHEN NEW.raw_user_meta_data ->> 'provider' = 'google' THEN TRUE
      ELSE FALSE 
    END,
    FALSE
  );

  -- If the user is a patient, create a patient record with safe defaults
  IF COALESCE(NEW.raw_user_meta_data ->> 'role', 'patient') = 'patient' THEN
    INSERT INTO public.patients (
      user_id,
      guardian_name,
      date_of_birth,
      autism_level,
      emergency_contact
    )
    VALUES (
      NEW.id,
      COALESCE(
        NEW.raw_user_meta_data ->> 'guardian_name',
        TRIM(CONCAT(
          COALESCE(NEW.raw_user_meta_data ->> 'first_name', ''), 
          ' ', 
          COALESCE(NEW.raw_user_meta_data ->> 'last_name', '')
        ))
      ),
      CASE 
        WHEN NEW.raw_user_meta_data ->> 'date_of_birth' IS NOT NULL 
        THEN (NEW.raw_user_meta_data ->> 'date_of_birth')::date 
        ELSE NULL 
      END,
      COALESCE((NEW.raw_user_meta_data ->> 'autism_level')::autism_level, '1'::autism_level),
      NEW.raw_user_meta_data ->> 'emergency_contact'
    );
  END IF;

  RETURN NEW;
END;
$$;

-- Ensure the trigger exists and is properly configured
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
