import React from 'react';
import { View, Text, StyleSheet, ViewStyle, TextStyle } from 'react-native';

export interface CardProps {
  children: React.ReactNode;
  style?: ViewStyle;
}

export interface CardHeaderProps {
  children: React.ReactNode;
  style?: ViewStyle;
}

export interface CardTitleProps {
  children: React.ReactNode;
  style?: TextStyle;
}

export interface CardDescriptionProps {
  children: React.ReactNode;
  style?: TextStyle;
}

export interface CardContentProps {
  children: React.ReactNode;
  style?: ViewStyle;
}

export interface CardFooterProps {
  children: React.ReactNode;
  style?: ViewStyle;
}

const Card: React.FC<CardProps> = ({ children, style }) => {
  const cardStyle: ViewStyle = {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
    borderWidth: 1,
    borderColor: '#F3F4F6',
  };

  return <View style={[cardStyle, style]}>{children}</View>;
};

const CardHeader: React.FC<CardHeaderProps> = ({ children, style }) => {
  const headerStyle: ViewStyle = {
    marginBottom: 12,
  };

  return <View style={[headerStyle, style]}>{children}</View>;
};

const CardTitle: React.FC<CardTitleProps> = ({ children, style }) => {
  const titleStyle: TextStyle = {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 4,
  };

  return <Text style={[titleStyle, style]}>{children}</Text>;
};

const CardDescription: React.FC<CardDescriptionProps> = ({ children, style }) => {
  const descriptionStyle: TextStyle = {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
  };

  return <Text style={[descriptionStyle, style]}>{children}</Text>;
};

const CardContent: React.FC<CardContentProps> = ({ children, style }) => {
  const contentStyle: ViewStyle = {
    flex: 1,
  };

  return <View style={[contentStyle, style]}>{children}</View>;
};

const CardFooter: React.FC<CardFooterProps> = ({ children, style }) => {
  const footerStyle: ViewStyle = {
    marginTop: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  };

  return <View style={[footerStyle, style]}>{children}</View>;
};

export { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter };
