{"common": {"loading": "Loading...", "error": "Error", "save": "Save", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "add": "Add", "search": "Search", "filter": "Filter", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "close": "Close", "yes": "Yes", "no": "No", "confirm": "Confirm", "dashboard": "Dashboard", "profile": "Profile", "settings": "Settings", "logout": "Logout", "signin": "Sign In", "signup": "Sign Up", "welcome": "Welcome", "home": "Home", "success": "Success"}, "auth": {"title": "Leeza AI", "subtitle": "Progress Tracking System", "patientPortal": "Patient Portal", "patientDescription": "Track your therapy progress and activities", "therapistPortal": "Therapist Portal", "therapistDescription": "Manage patients and therapy programs", "adminPortal": "Admin Portal", "adminDescription": "System administration and management", "email": "Email", "password": "Password", "firstName": "First Name", "lastName": "Last Name", "guardianName": "Guardian Name", "dateOfBirth": "Date of Birth", "autismLevel": "Autism Level", "emergencyContact": "Emergency Contact", "signingIn": "Signing in...", "creatingAccount": "Creating account...", "signUpAs": "Sign Up as {{role}}", "checkEmail": "Check your email for verification link.", "welcomeBack": "Welcome back!", "signedInSuccessfully": "You have been signed in successfully."}, "dashboard": {"welcomeBack": "Welcome back, {{name}}!", "overview": "Overview", "quickActions": "Quick Actions", "recentActivity": "Recent Activity", "statistics": "Statistics"}, "patient": {"goals": "Goals", "activities": "Activities", "sessions": "Sessions", "milestones": "Milestones", "game": "Skills Game", "therapyGoals": "Therapy Goals", "myActivities": "My Activities", "mySessions": "My Sessions", "myMilestones": "My Milestones", "progress": "Progress", "completed": "Completed", "active": "Active", "scheduled": "Scheduled", "noGoalsDate": "No Goals for This Date", "noGoalsDateDescription": "No therapy goals are scheduled for {{date}}.", "aiInsights": "AI Insights", "totalGoals": "Total Goals", "activeToday": "Active Today", "goalsForDate": "Goals for Selected Date", "sessionDetails": "Session Details", "duration": "Duration", "therapyType": "Therapy Type", "therapist": "Therapist", "virtual": "Virtual", "inPerson": "In-Person", "target": "Target", "category": "Category", "defaultName": "Patient", "yourTherapist": "Your therapist: {{name}}", "waitingTherapistAssignment": "Your account has been created! Waiting for therapist assignment.", "pendingTherapistAssignment": "Pending Therapist Assignment", "therapistAssigned": "Therapist Assigned", "leezaAIAssistant": "Leeza AI Assistant", "aiCompanionDescription": "Your personal AI companion for support & guidance", "available247": "Available 24/7", "aiPowered": "AI Powered", "autismLevel": "Autism Level", "activeGoalsCount": "{{count}} active goals", "completedTodayCount": "{{completed}}/{{total}} completed today", "achievedCount": "{{count}} achieved", "upcomingCount": "{{count}} upcoming", "gameDescription": "Play games to improve motor, cognitive & speech skills", "new": "New!", "noMilestones": "No Milestones Yet", "noMilestonesDescription": "Your therapist will track important milestones in your progress.", "achievedOn": "Achieved on", "sessionPreparation": "AI Session Preparation", "noSessions": "No Sessions Scheduled", "noSessionsDescription": "Your therapist will schedule sessions for you. Check back later.", "session": "Session", "focusAreas": "Focus Areas", "minutes": "minutes", "joinSession": "Join Session", "chooseGameMode": "Choose Game Mode", "score": "Score", "level": "Level", "start": "Start", "sayTheWord": "Say the word", "startSpeaking": "Start Speaking", "tapOnlyColor": "Tap only", "shapes": "shapes", "howToPlay": "How to Play", "motorInstructions": "Tap the shapes as quickly as possible to improve hand-eye coordination", "cognitiveInstructions": "Only tap shapes of the specified color to challenge your focus and attention", "speechInstructions": "Say the word clearly into your microphone to practice speech and pronunciation", "noTherapistAssigned": "No Therapist Assigned", "contactCareCoordinator": "Contact your care coordinator for assignment", "pending": "Pending", "assignedTherapist": "Your Assigned Therapist", "assigned": "Assigned", "noSessionScheduled": "No session scheduled for {{date}}"}, "therapist": {"patients": "My Patients", "patientManagement": "Patient Management", "goalsManagement": "Goals Management", "activitiesManagement": "Activities Management", "sessionsManagement": "Sessions Management", "totalPatients": "Total Patients", "activeSessions": "Active Sessions", "completedGoals": "Completed Goals", "upcomingSessions": "Upcoming Sessions", "manageAndTrackPatients": "Manage and track your assigned patients", "assignedToYou": "Assigned to you", "activeGoals": "Active Goals", "totalGoals": "Total goals", "avgCompletion": "Avg Completion", "activityCompletion": "Activity completion", "patientOverview": "Patient Overview", "clickToManage": "Click on any patient to manage their therapy plan", "noPatientsAssigned": "No Patients Assigned", "contactAdministrator": "Contact your administrator to get patients assigned to you.", "yearsOld": "{{age}} years old", "complete": "Complete", "selectPatient": "Select patient", "unknownPatient": "Unknown Patient", "searchActivities": "Search activities...", "createActivity": "Create Activity", "createNewActivity": "Create New Activity", "activityTitle": "Activity title", "activityDescription": "Activity description", "selectCategory": "Select category", "selfCare": "Self Care", "academic": "Academic", "scheduledTime": "Scheduled time", "startDate": "Start date", "endDate": "End date", "noActivitiesCreated": "No Activities Created", "startByCreatingActivities": "Start by creating daily activities for your patients.", "inactive": "Inactive", "anytime": "Anytime", "patient": "Patient"}, "admin": {"patientManagement": "Patient Management", "therapistManagement": "Therapist Management", "systemReports": "System Reports", "totalUsers": "Total Users", "activeTherapists": "Active Therapists", "totalPatients": "Total Patients", "systemHealth": "System Health", "failedToFetchTherapists": "Failed to fetch therapists", "therapistProfile": "Therapist Profile", "managingPatients": "Managing {{count}} patients", "searchTherapists": "Search therapists...", "addTherapist": "Add Therapist", "noTherapistsFound": "No Therapists Found", "noTherapistsMatch": "No therapists match your search criteria.", "noTherapistsRegistered": "No therapists have been registered yet.", "patientCount": "{{count}} Patients", "joined": "Joined", "noPatientsAssigned": "No patients assigned", "viewProfile": "View Profile", "manage": "Manage", "failedToFetchData": "Failed to fetch data", "patientProfile": "Patient Profile", "searchPatients": "Search patients...", "addPatient": "Add Patient", "noPatientsFound": "No Patients Found", "noPatientsMatch": "No patients match your search criteria.", "noPatientsRegistered": "No patients have been registered yet.", "guardian": "Guardian", "notSpecified": "Not specified", "registered": "Registered", "assignedToTherapist": "Assigned to therapist", "awaitingTherapistAssignment": "Awaiting therapist assignment", "comprehensiveAnalytics": "Comprehensive analytics and insights", "aiSystemAnalysis": "AI System Analysis", "totalSessions": "Total Sessions", "goalCompletion": "Goal Completion", "fromLastMonth": "+{{count}} from last month", "thisWeekIncrease": "+12% this week", "percentFromLastMonth": "+{{percent}}% from last month", "therapyGoalsProgress": "Therapy Goals Progress", "goalCompletionOverview": "Overview of goal completion status", "completedGoals": "Completed Goals", "activityCompletion": "Activity Completion", "milestonesAchieved": "Milestones Achieved", "milestoneStatus": "Milestone Status", "milestoneProgressDistribution": "Current milestone progress distribution", "regressed": "Regressed", "sessionTrends": "Session Trends", "monthlySessionDistribution": "Monthly session distribution", "goalStatusDistribution": "Goal Status Distribution", "currentGoalStatusBreakdown": "Current goal status breakdown", "managePatient": "Manage Patient", "patientName": "Patient Name", "assignedTherapist": "Assigned Therapist", "selectTherapist": "Select a therapist", "noTherapist": "No therapist assigned", "medicalNotes": "Medical Notes", "medicalNotesPlaceholder": "Enter any relevant medical notes or observations...", "emergencyContactPlaceholder": "Enter emergency contact information...", "updating": "Updating...", "updatePatient": "Update Patient", "patientUpdatedSuccessfully": "Patient updated successfully", "failedToUpdatePatient": "Failed to update patient", "manageTherapist": "Manage Therapist", "updateTherapist": "Update Therapist", "therapistUpdatedSuccessfully": "Therapist updated successfully", "failedToUpdateTherapist": "Failed to update therapist"}, "therapyTypes": {"aba_therapy": "ABA Therapy", "speech_therapy": "Speech Therapy", "occupational_therapy": "Occupational Therapy", "behavioral_therapy": "Behavioral Therapy"}, "categories": {"communication": "Communication", "social": "Social", "behavior": "Behavior", "cognitive": "Cognitive", "motor": "Motor"}, "status": {"active": "Active", "completed": "Completed", "paused": "Paused", "pending": "Pending", "cancelled": "Cancelled", "in_progress": "In Progress", "scheduled": "Scheduled", "confirmed": "Confirmed", "rescheduled": "Rescheduled"}, "autismLevels": {"1": "Level 1", "2": "Level 2", "3": "Level 3"}, "days": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}, "months": {"january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December"}}