
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const GEMINI_API_KEY = Deno.env.get('GEMINI_API_KEY');
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

function extractJson(text: string): any {
  let jsonString = text;

  // Strip code block (```json ... ```)
  const codeBlockMatch = text.match(/```(?:json)?\n?([\s\S]*?)```/i);
  if (codeBlockMatch) {
    jsonString = codeBlockMatch[1];
  }

  // Find first and last curly braces
  const braceIdxStart = jsonString.indexOf("{");
  const braceIdxEnd = jsonString.lastIndexOf("}");
  if (braceIdxStart !== -1 && braceIdxEnd !== -1 && braceIdxEnd > braceIdxStart) {
    jsonString = jsonString.slice(braceIdxStart, braceIdxEnd + 1);
  }

  // Final fallback: regex for JSON object
  const regexJson = /{[\s\S]*}/;
  const regexMatch = jsonString.match(regexJson);
  if (regexMatch) {
    jsonString = regexMatch[0];
  }

  try {
    return JSON.parse(jsonString);
  } catch (e) {
    throw new Error("Failed to parse JSON from AI output. Raw output: " + text.slice(0, 400));
  }
}

function buildSystemPrompt(context: any, transcript: string) {
  const currentPath = context?.path || "/";
  const userRole = context?.role || "therapist";
  const recentCommands = context?.recentCommands || [];
  
  return `
You are Leeza, an EXTREMELY INTELLIGENT Context-Aware Voice Agent for autism therapy management.

CURRENT CONTEXT:
- Location: ${currentPath}
- User Role: ${userRole}
- Recent Commands: ${recentCommands.map(cmd => cmd.action).join(", ")}

THERAPIST ACTIONS:
- view_patients: Show all patients
- view_patient: View specific patient profile
- view_goals: Show therapy goals
- create_goal: Create new therapy goal
- view_sessions: Show therapy sessions
- schedule_session: Schedule new session
- view_activities: Show daily activities
- update_progress: Update patient progress
- generate_report: Generate patient report
- navigate: Navigate to specific page
- help: Show available commands
- sign_out: Logout from system

PARAMETER EXTRACTION:
- Patient Names: Extract from "for [name]", "with [name]", "[name]'s", "patient [name]"
- Progress: Extract percentages or descriptive terms (75%, "almost done" = 90%)
- Time: "today", "tomorrow", "next week", "morning/afternoon/evening"

RESPONSE FORMAT (JSON):
{
  "action": "[ACTION_TYPE]",
  "target": "[TARGET_IF_ANY]",
  "parameters": {
    "patient_name": "extracted name or null",
    "datetime": "relative time or null",
    "progress_percentage": "number 0-100 or null",
    "goal_title": "extracted goal or null",
    "session_type": "therapy/evaluation/consultation or null"
  },
  "response": "Clear, helpful response acknowledging what will happen",
  "confidence": "high/medium/low",
  "clarification_needed": "null or specific question if unclear"
}

Parse this therapist command: "${transcript}"
`;
}

serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    const { transcript, context } = await req.json();

    if (!transcript) {
      console.error("Missing transcript in request");
      return new Response(
        JSON.stringify({ error: "Missing transcript" }),
        {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Check if API key exists
    if (!GEMINI_API_KEY) {
      console.error("GEMINI_API_KEY not found in environment variables");
      return new Response(
        JSON.stringify({ 
          error: "Gemini API key not configured",
          action: "help",
          response: "Voice processing temporarily unavailable. Please try again later.",
          confidence: "low"
        }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    const systemPrompt = buildSystemPrompt(context, transcript);
    console.log("Processing transcript:", transcript);

    const geminiResp = await fetch(
      `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=${GEMINI_API_KEY}`,
      {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          contents: [
            { parts: [{ text: systemPrompt }] },
          ],
          generationConfig: {
            temperature: 0.1,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 1024,
          },
        }),
      }
    );

    console.log("Gemini API response status:", geminiResp.status);

    if (!geminiResp.ok) {
      const errorText = await geminiResp.text();
      console.error("Gemini API error:", errorText);
      
      // Return a fallback response instead of throwing
      return new Response(
        JSON.stringify({
          action: "help",
          response: "I couldn't understand that command. Please try rephrasing or say 'help' for available commands.",
          confidence: "low",
          error: "Command parsing failed"
        }),
        {
          status: 200, // Return 200 with fallback instead of 500
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }
    
    const data = await geminiResp.json();
    console.log("Gemini API response data:", JSON.stringify(data, null, 2));
    
    let parsedCommand;
    
    try {
      const rawText = data?.candidates?.[0]?.content?.parts?.[0]?.text || "";
      console.log("Raw AI response:", rawText);
      
      if (!rawText) {
        throw new Error("Empty response from AI");
      }
      
      parsedCommand = extractJson(rawText);
      console.log("Parsed command:", parsedCommand);
    } catch (e) {
      console.error("Failed to extract JSON:", e.message);
      
      // Return a fallback response
      return new Response(
        JSON.stringify({
          action: "help",
          response: "I couldn't process that command. Please try rephrasing or say 'help' for available commands.",
          confidence: "low",
          error: "JSON parsing failed"
        }),
        {
          status: 200,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Validate the parsed command has required fields
    if (!parsedCommand || !parsedCommand.action) {
      console.error("Invalid command structure:", parsedCommand);
      return new Response(
        JSON.stringify({
          action: "help",
          response: "I couldn't understand that command. Please try rephrasing.",
          confidence: "low"
        }),
        {
          status: 200,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    return new Response(
      JSON.stringify(parsedCommand),
      { headers: { ...corsHeaders, "Content-Type": "application/json" } }
    );
  } catch (error: any) {
    console.error("Unexpected error in therapist-command-parser:", error);
    return new Response(
      JSON.stringify({
        action: "help",
        response: "Something went wrong processing your command. Please try again.",
        confidence: "low",
        error: error.message
      }),
      {
        status: 200, // Return 200 with fallback instead of 500
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      }
    );
  }
});
