
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Clock, Calendar, Video, MapPin, User } from "lucide-react";
import { cn } from "@/lib/utils";

interface SessionDetailsCardProps {
  title?: string;
  performedOn?: string;
  duration?: number;
  therapyType?: string;
  isVirtual?: boolean;
  status?: string;
  therapistName?: string;
  className?: string;
}

export function SessionDetailsCard({
  title = "Session Details",
  performedOn,
  duration,
  therapyType,
  isVirtual,
  status,
  therapistName,
  className,
}: SessionDetailsCardProps) {
  const formatDuration = (minutes?: number) => {
    if (!minutes) return "Not specified";
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;

    if (hours > 0) {
      return `${hours} hr ${remainingMinutes > 0 ? `${remainingMinutes} min` : ''}`;
    }
    return `${minutes} min`;
  };

  const getTherapyTypeDisplay = (type?: string) => {
    if (!type) return "Not specified";
    return type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const getModeDisplay = (virtual?: boolean) => {
    return virtual ? "Virtual" : "In-Person";
  };

  return (
    <Card className={cn("bg-gray-50 border-0", className)}>
      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
            {performedOn && (
              <p className="text-sm text-gray-600 mt-1">
                Performed on {performedOn}
              </p>
            )}
          </div>
          {status && (
            <Badge
              variant="secondary"
              className={cn(
                "border-0",
                isVirtual
                  ? "bg-purple-100 text-purple-700"
                  : "bg-green-100 text-green-700"
              )}
            >
              <div className="flex items-center space-x-1">
                {isVirtual ? (
                  <Video className="h-3 w-3" />
                ) : (
                  <MapPin className="h-3 w-3" />
                )}
                <span>{getModeDisplay(isVirtual)}</span>
              </div>
            </Badge>
          )}
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-gray-600 mb-1">Duration</p>
            <div className="flex items-center space-x-1">
              <Clock className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-900">
                {formatDuration(duration)}
              </span>
            </div>
          </div>
          <div>
            <p className="text-sm text-gray-600 mb-1">Therapy Type</p>
            <span className="text-sm font-medium text-gray-900">
              {getTherapyTypeDisplay(therapyType)}
            </span>
          </div>
        </div>

        {therapistName && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <p className="text-sm text-gray-600 mb-1">Therapist</p>
            <div className="flex items-center space-x-1">
              <User className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-900">
                {therapistName}
              </span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
