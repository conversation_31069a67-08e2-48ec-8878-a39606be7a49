import React from 'react';
import { TextInput, StyleSheet, ViewStyle, TextStyle } from 'react-native';

export interface InputProps {
  value?: string;
  onChangeText?: (text: string) => void;
  placeholder?: string;
  secureTextEntry?: boolean;
  keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad';
  autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters';
  editable?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  multiline?: boolean;
  numberOfLines?: number;
}

const Input: React.FC<InputProps> = ({
  value,
  onChangeText,
  placeholder,
  secureTextEntry = false,
  keyboardType = 'default',
  autoCapitalize = 'sentences',
  editable = true,
  style,
  textStyle,
  multiline = false,
  numberOfLines = 1,
}) => {
  const inputStyle: ViewStyle = {
    height: multiline ? undefined : 40,
    minHeight: multiline ? 80 : 40,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: '#FFFFFF',
    fontSize: 14,
    color: '#374151',
  };

  const defaultTextStyle: TextStyle = {
    fontSize: 14,
    color: '#374151',
  };

  return (
    <TextInput
      style={[inputStyle, style, defaultTextStyle, textStyle]}
      value={value}
      onChangeText={onChangeText}
      placeholder={placeholder}
      placeholderTextColor="#9CA3AF"
      secureTextEntry={secureTextEntry}
      keyboardType={keyboardType}
      autoCapitalize={autoCapitalize}
      editable={editable}
      multiline={multiline}
      numberOfLines={numberOfLines}
    />
  );
};

export { Input };
