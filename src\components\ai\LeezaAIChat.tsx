
import { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { ArrowLeft, Send, Search, MessageSquare, Heart, AlertTriangle, TrendingUp, Plus, History } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/hooks/useAuth";
import { toast } from "sonner";

interface Message {
  id: string;
  content: string;
  is_user: boolean;
  created_at: string;
}

interface ChatSession {
  id: string;
  title: string;
  mode: string;
  created_at: string;
  updated_at: string;
}

const modeConfig = {
  caregiver: { icon: Heart, color: "bg-pink-500", label: "Caregiver" },
  therapy: { icon: MessageSquare, color: "bg-blue-500", label: "Therapy" },
  panic: { icon: AlertTriangle, color: "bg-red-500", label: "Panic Mode" },
  progress: { icon: TrendingUp, color: "bg-green-500", label: "Progress" }
};

interface LeezaAIChatProps {
  isOpen: boolean;
  onClose: () => void;
}

export function LeezaAIChat({ isOpen, onClose }: LeezaAIChatProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [currentMode, setCurrentMode] = useState<string>("caregiver");
  const [currentSession, setCurrentSession] = useState<string | null>(null);
  const [chatSessions, setChatSessions] = useState<ChatSession[]>([]);
  const [showHistory, setShowHistory] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { profile } = useAuth();

  useEffect(() => {
    if (isOpen) {
      fetchChatSessions();
    }
  }, [isOpen]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const fetchChatSessions = async () => {
    try {
      const { data: patientData } = await supabase
        .from("patients")
        .select("id")
        .eq("user_id", profile?.id)
        .single();

      if (patientData) {
        const { data: sessions } = await supabase
          .from("chat_sessions")
          .select("*")
          .eq("patient_id", patientData.id)
          .order("updated_at", { ascending: false });

        setChatSessions(sessions || []);
      }
    } catch (error) {
      console.error("Error fetching chat sessions:", error);
    }
  };

  const loadChatSession = async (sessionId: string) => {
    try {
      const { data: messages } = await supabase
        .from("chat_messages")
        .select("*")
        .eq("chat_session_id", sessionId)
        .order("created_at", { ascending: true });

      setMessages(messages || []);
      setCurrentSession(sessionId);
      setShowHistory(false);

      const session = chatSessions.find(s => s.id === sessionId);
      if (session) {
        setCurrentMode(session.mode);
      }
    } catch (error) {
      console.error("Error loading chat session:", error);
      toast.error("Failed to load chat session");
    }
  };

  const createNewSession = async () => {
    try {
      const { data: patientData } = await supabase
        .from("patients")
        .select("id")
        .eq("user_id", profile?.id)
        .single();

      if (!patientData) {
        toast.error("Patient data not found");
        return;
      }

      const title = `${modeConfig[currentMode as keyof typeof modeConfig].label} Chat - ${new Date().toLocaleDateString()}`;

      const { data: newSession } = await supabase
        .from("chat_sessions")
        .insert({
          patient_id: patientData.id,
          title,
          mode: currentMode
        })
        .select()
        .single();

      if (newSession) {
        setCurrentSession(newSession.id);
        setMessages([]);
        await fetchChatSessions();
        toast.success("New chat session created");
      }
    } catch (error) {
      console.error("Error creating session:", error);
      toast.error("Failed to create new session");
    }
  };

  const generateSessionTitle = (firstMessage: string): string => {
    const truncated = firstMessage.length > 30 ? firstMessage.substring(0, 30) + "..." : firstMessage;
    return `${modeConfig[currentMode as keyof typeof modeConfig].label}: ${truncated}`;
  };

  const updateSessionTitle = async (sessionId: string, title: string) => {
    try {
      await supabase
        .from("chat_sessions")
        .update({ title, updated_at: new Date().toISOString() })
        .eq("id", sessionId);
    } catch (error) {
      console.error("Error updating session title:", error);
    }
  };

  const saveMessage = async (content: string, isUser: boolean) => {
    if (!currentSession) return;

    try {
      const { data } = await supabase
        .from("chat_messages")
        .insert({
          chat_session_id: currentSession,
          content,
          is_user: isUser
        })
        .select()
        .single();

      return data;
    } catch (error) {
      console.error("Error saving message:", error);
    }
  };

  const sendMessage = async () => {
    if (!inputMessage.trim()) return;

    if (!currentSession) {
      await createNewSession();
      if (!currentSession) return;
    }

    const userMessage = inputMessage.trim();
    setInputMessage("");
    setIsLoading(true);

    // Add user message
    const newUserMessage: Message = {
      id: Date.now().toString(),
      content: userMessage,
      is_user: true,
      created_at: new Date().toISOString()
    };

    setMessages(prev => [...prev, newUserMessage]);
    await saveMessage(userMessage, true);

    // Update session title if this is the first message
    if (messages.length === 0) {
      const title = generateSessionTitle(userMessage);
      await updateSessionTitle(currentSession, title);
      await fetchChatSessions();
    }

    try {
      // Check if message might need search
      const needsSearch = userMessage.toLowerCase().includes('search') || 
                         userMessage.toLowerCase().includes('find') ||
                         userMessage.toLowerCase().includes('research') ||
                         userMessage.includes('?');

      const response = await supabase.functions.invoke('leeza-ai-chat', {
        body: {
          message: userMessage,
          mode: currentMode,
          sessionId: currentSession,
          needsSearch
        }
      });

      if (response.error) throw response.error;

      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: response.data.response,
        is_user: false,
        created_at: new Date().toISOString()
      };

      setMessages(prev => [...prev, aiMessage]);
      await saveMessage(response.data.response, false);
    } catch (error) {
      console.error("Error sending message:", error);
      toast.error("Failed to send message");
    } finally {
      setIsLoading(false);
    }
  };

  const filteredSessions = chatSessions.filter(session =>
    session.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    session.mode.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const ModeIcon = modeConfig[currentMode as keyof typeof modeConfig]?.icon || MessageSquare;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl h-[80vh] p-0">
        <DialogHeader className="p-6 pb-0">
          <DialogTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className={`w-8 h-8 rounded-full ${modeConfig[currentMode as keyof typeof modeConfig]?.color} flex items-center justify-center`}>
                <ModeIcon className="h-4 w-4 text-white" />
              </div>
              <span>Leeza AI</span>
              <Badge variant="outline">{modeConfig[currentMode as keyof typeof modeConfig]?.label}</Badge>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowHistory(!showHistory)}
              >
                <History className="h-4 w-4 mr-1" />
                History
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={createNewSession}
              >
                <Plus className="h-4 w-4 mr-1" />
                New Chat
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="flex flex-1 overflow-hidden">
          {/* Chat History Sidebar */}
          {showHistory && (
            <div className="w-80 border-r p-4 overflow-y-auto">
              <div className="space-y-4">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search chats..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <div className="space-y-2">
                  {filteredSessions.map((session) => {
                    const SessionIcon = modeConfig[session.mode as keyof typeof modeConfig]?.icon || MessageSquare;
                    return (
                      <Card
                        key={session.id}
                        className={`cursor-pointer transition-colors hover:bg-gray-50 ${
                          currentSession === session.id ? 'ring-2 ring-blue-500' : ''
                        }`}
                        onClick={() => loadChatSession(session.id)}
                      >
                        <CardContent className="p-3">
                          <div className="flex items-start space-x-2">
                            <div className={`w-6 h-6 rounded-full ${modeConfig[session.mode as keyof typeof modeConfig]?.color} flex items-center justify-center flex-shrink-0`}>
                              <SessionIcon className="h-3 w-3 text-white" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium truncate">{session.title}</p>
                              <p className="text-xs text-gray-500">
                                {new Date(session.updated_at).toLocaleDateString()}
                              </p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              </div>
            </div>
          )}

          {/* Main Chat Area */}
          <div className="flex-1 flex flex-col">
            {/* Mode Selector */}
            <div className="p-4 border-b">
              <Select value={currentMode} onValueChange={setCurrentMode}>
                <SelectTrigger className="w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(modeConfig).map(([mode, config]) => {
                    const Icon = config.icon;
                    return (
                      <SelectItem key={mode} value={mode}>
                        <div className="flex items-center space-x-2">
                          <div className={`w-4 h-4 rounded-full ${config.color} flex items-center justify-center`}>
                            <Icon className="h-2 w-2 text-white" />
                          </div>
                          <span>{config.label}</span>
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>

            {/* Messages */}
            <ScrollArea className="flex-1 p-4">
              <div className="space-y-4">
                {messages.length === 0 && (
                  <div className="text-center text-gray-500 py-8">
                    <ModeIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <p>Start a conversation with Leeza AI</p>
                    <p className="text-sm">I'm here to help with {modeConfig[currentMode as keyof typeof modeConfig]?.label.toLowerCase()} support</p>
                  </div>
                )}

                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.is_user ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-[80%] p-3 rounded-lg ${
                        message.is_user
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-100 text-gray-900'
                      }`}
                    >
                      <p className="whitespace-pre-wrap">{message.content}</p>
                      <p className={`text-xs mt-1 ${
                        message.is_user ? 'text-blue-100' : 'text-gray-500'
                      }`}>
                        {new Date(message.created_at).toLocaleTimeString()}
                      </p>
                    </div>
                  </div>
                ))}

                {isLoading && (
                  <div className="flex justify-start">
                    <div className="bg-gray-100 p-3 rounded-lg">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                      </div>
                    </div>
                  </div>
                )}
                <div ref={messagesEndRef} />
              </div>
            </ScrollArea>

            {/* Input Area */}
            <div className="p-4 border-t">
              <div className="flex space-x-2">
                <Input
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  placeholder={`Message Leeza AI (${modeConfig[currentMode as keyof typeof modeConfig]?.label} mode)...`}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      sendMessage();
                    }
                  }}
                  disabled={isLoading}
                />
                <Button onClick={sendMessage} disabled={isLoading || !inputMessage.trim()}>
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
