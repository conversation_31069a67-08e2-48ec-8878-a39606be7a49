
-- 1. Create video_sessions table for signaling and call tracking
CREATE TABLE public.video_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  room_id TEXT NOT NULL,
  created_by UUID NOT NULL,
  participants UUID[] NOT NULL,
  status TEXT NOT NULL CHECK (status IN ('waiting', 'active', 'ended')),
  started_at TIMESTAMP WITH TIME ZONE,
  ended_at TIMESTAMP WITH TIME ZONE,
  therapy_session_id UUID,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- 2. Allow real-time streaming for video_sessions
ALTER TABLE public.video_sessions REPLICA IDENTITY FULL;
ALTER PUBLICATION supabase_realtime ADD TABLE public.video_sessions;

-- 3. Enable Row-Level Security
ALTER TABLE public.video_sessions ENABLE ROW LEVEL SECURITY;

-- 4. Only allow participants or creator to select/view a call
CREATE POLICY "Participants can view their video sessions" ON public.video_sessions
  FOR SELECT USING (
    auth.uid() = created_by OR participants @> ARRAY[auth.uid()]
  );

-- 5. Allow creator/patient/therapist to insert a video session for calls they're part of
CREATE POLICY "Allowed users can insert their video session" ON public.video_sessions
  FOR INSERT WITH CHECK (
    auth.uid() = created_by OR ARRAY[auth.uid()] <@ participants
  );

-- 6. Allow only participants to update (for status signaling)
CREATE POLICY "Participants can update their video session" ON public.video_sessions
  FOR UPDATE USING (
    auth.uid() = created_by OR participants @> ARRAY[auth.uid()]
  );

-- 7. Add a trigger to update updated_at on row changes
CREATE TRIGGER update_updated_at BEFORE UPDATE ON public.video_sessions
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

-- 8. Indexes
CREATE INDEX idx_video_sessions_room_id ON public.video_sessions(room_id);
CREATE INDEX idx_video_sessions_status ON public.video_sessions(status);
CREATE INDEX idx_video_sessions_participants ON public.video_sessions USING GIN (participants);

