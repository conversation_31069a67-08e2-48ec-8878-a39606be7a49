
import { Card, CardContent } from "@/components/ui/card";
import { Target } from "lucide-react";
import { format } from "date-fns";
import { useTranslation } from "react-i18next";

interface GoalsEmptyStateProps {
  selectedDate: Date;
}

export function GoalsEmptyState({ selectedDate }: GoalsEmptyStateProps) {
  const { t } = useTranslation();

  return (
    <Card className="app-card shadow-lg rounded-3xl">
      <CardContent className="p-12 text-center">
        <Target className="h-16 w-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-gray-900 mb-2">{t('patient.noGoalsDate')}</h3>
        <p className="text-gray-600">
          {t('patient.noGoalsDateDescription', { date: format(selectedDate, "MMMM do, yyyy") })}
        </p>
      </CardContent>
    </Card>
  );
}
