
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Title } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "react-i18next";

interface Patient {
  id: string;
  guardian_name: string;
  date_of_birth: string;
  autism_level: string;
  assigned_therapist_id: string;
  medical_notes?: string;
  emergency_contact?: string;
  user?: {
    first_name: string;
    last_name: string;
  };
}

interface Therapist {
  id: string;
  first_name: string;
  last_name: string;
}

interface PatientManageModalProps {
  isOpen: boolean;
  onClose: () => void;
  patient: Patient | null;
  therapists: Therapist[];
  onUpdate: () => void;
}

export function PatientManageModal({ isOpen, onClose, patient, therapists, onUpdate }: PatientManageModalProps) {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    assigned_therapist_id: "",
    medical_notes: "",
    emergency_contact: "",
  });
  const { toast } = useToast();
  const { t } = useTranslation();

  useEffect(() => {
    if (patient) {
      setFormData({
        assigned_therapist_id: patient.assigned_therapist_id || "no_therapist",
        medical_notes: patient.medical_notes || "",
        emergency_contact: patient.emergency_contact || "",
      });
    }
  }, [patient]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!patient) return;

    setLoading(true);
    try {
      const updateData = {
        ...formData,
        assigned_therapist_id: formData.assigned_therapist_id === "no_therapist" ? null : formData.assigned_therapist_id,
      };

      const { error } = await supabase
        .from("patients")
        .update(updateData)
        .eq("id", patient.id);

      if (error) throw error;

      toast({
        title: t("common.success"),
        description: t("admin.patientUpdatedSuccessfully"),
      });
      onUpdate();
      onClose();
    } catch (error: any) {
      toast({
        title: t("common.error"),
        description: t("admin.failedToUpdatePatient"),
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{t("admin.managePatient")}</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="patient-name">{t("admin.patientName")}</Label>
            <Input
              id="patient-name"
              value={`${patient?.user?.first_name || ""} ${patient?.user?.last_name || ""}`}
              disabled
              className="bg-gray-50"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="therapist">{t("admin.assignedTherapist")}</Label>
            <Select
              value={formData.assigned_therapist_id}
              onValueChange={(value) => setFormData({ ...formData, assigned_therapist_id: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder={t("admin.selectTherapist")} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="no_therapist">{t("admin.noTherapist")}</SelectItem>
                {therapists.map((therapist) => (
                  <SelectItem key={therapist.id} value={therapist.id}>
                    {therapist.first_name} {therapist.last_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="medical-notes">{t("admin.medicalNotes")}</Label>
            <Textarea
              id="medical-notes"
              placeholder={t("admin.medicalNotesPlaceholder")}
              value={formData.medical_notes}
              onChange={(e) => setFormData({ ...formData, medical_notes: e.target.value })}
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="emergency-contact">{t("admin.emergencyContact")}</Label>
            <Input
              id="emergency-contact"
              placeholder={t("admin.emergencyContactPlaceholder")}
              value={formData.emergency_contact}
              onChange={(e) => setFormData({ ...formData, emergency_contact: e.target.value })}
            />
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              {t("common.cancel")}
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? t("admin.updating") : t("admin.updatePatient")}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
