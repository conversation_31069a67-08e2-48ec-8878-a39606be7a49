import { useState } from "react";
import { useAuth } from "@/hooks/useAuth";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { LeezaAIChat } from "@/components/ai/LeezaAIChat";
import DailyEntryForm from "./DailyEntryForm";
import { useNavigate } from "react-router-dom";
import { Gamepad2, Sparkles } from "lucide-react";
import { Button } from "@/components/ui/button";
import { LeezaAIVoiceAgent } from "@/components/ai/LeezaAIVoiceAgent";

export default function TherapistDashboard() {
  const { profile } = useAuth();
  const [showLeeza, setShowLeeza] = useState(false);
  const navigate = useNavigate();

  // TO-DO: add patient grid, sessions, quick summary (minimal here for brevity)

  return (
    <div className="min-h-screen bg-gray-50 py-6 px-2 sm:px-0">
      <div className="max-w-4xl mx-auto space-y-8">
        <Card>
          <CardHeader>
            <CardTitle>Welcome, {profile?.first_name || "Therapist"}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-gray-600 mb-2">
              Manage your cases and record your patients' progress—all on one screen.
            </div>
          </CardContent>
        </Card>
        <div className="flex gap-4">
          <Button
            className="flex-1 bg-app-purple text-white"
            onClick={() => setShowLeeza(true)}
          >
            <Sparkles className="mr-2 h-5 w-5" />
            Leeza AI Assistant
          </Button>
          <Button
            className="flex-1 bg-indigo-600 text-white"
            onClick={() => navigate("/patient/game")}
          >
            <Gamepad2 className="mr-2 h-5 w-5" />
            Skills Game
          </Button>
        </div>
        {/* Unified Data Entry */}
        <DailyEntryForm isTherapist />
        <LeezaAIChat isOpen={showLeeza} onClose={() => setShowLeeza(false)} />

        {/* Leeza AI Voice Agent -- new for demo */}
        <LeezaAIVoiceAgent />
      </div>
    </div>
  );
}
