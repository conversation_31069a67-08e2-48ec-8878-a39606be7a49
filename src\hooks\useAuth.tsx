
import { createContext, useContext, useEffect, useState, useRef } from "react";
import { User } from "@supabase/supabase-js";
import { supabase } from "@/integrations/supabase/client";

interface Profile {
  id: string;
  first_name: string | null;
  last_name: string | null;
  email: string;
  role: string;
  google_id: string | null;
}

interface AuthContextType {
  user: User | null;
  profile: Profile | null;
  isAuthenticated: boolean;
  loading: boolean;
  hasProfile: boolean;
  userRole: string | null;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [authLoading, setAuthLoading] = useState(true);
  const [profileLoading, setProfileLoading] = useState(false);
  const mountedRef = useRef(true);
  const initializedRef = useRef(false);

  const fetchProfile = async (userId: string, retryCount = 0) => {
    const maxRetries = 3;
    
    try {
      console.log(`🔍 Fetching profile for user: ${userId} (attempt ${retryCount + 1})`);
      setProfileLoading(true);

      const { data, error } = await supabase
        .from("profiles")
        .select("id, first_name, last_name, email, role, google_id")
        .eq("id", userId)
        .single();

      if (!mountedRef.current) return;

      if (error) {
        if (error.code === 'PGRST116') {
          console.log("ℹ️ No profile found - this may be expected for new users");
        } else {
          console.error("Profile fetch error:", error);
        }
        
        // Retry for network errors but not for "not found" errors
        if (error.code !== 'PGRST116' && retryCount < maxRetries) {
          console.log(`🔄 Retrying profile fetch in 1 second (${retryCount + 1}/${maxRetries})`);
          setTimeout(() => {
            fetchProfile(userId, retryCount + 1);
          }, 1000);
          return;
        }
        
        setProfile(null);
        return;
      }

      console.log("✅ Profile found:", data);
      
      // Check if this is a Google OAuth therapist that needs role correction
      const intendedRole = localStorage.getItem('intended_role');
      if (intendedRole === 'therapist' && data.role === 'patient' && data.google_id) {
        console.log("🔧 Correcting role for Google OAuth therapist");
        
        try {
          // Update the profile role to therapist
          const { error: updateError } = await supabase
            .from('profiles')
            .update({ role: 'therapist' })
            .eq('id', userId);
            
          if (!updateError) {
            // Update auth_flows as well
            await supabase
              .from('auth_flows')
              .update({ flow_type: 'therapist' })
              .eq('user_id', userId);
              
            // Remove the patient record if it exists
            await supabase
              .from('patients')
              .delete()
              .eq('user_id', userId);
              
            // Update the profile data locally
            data.role = 'therapist';
            console.log("✅ Role corrected to therapist");
          }
        } catch (correctionError) {
          console.error("Error correcting role:", correctionError);
        }
        
        // Clean up localStorage
        localStorage.removeItem('intended_role');
      }
      
      setProfile(data);
    } catch (error) {
      console.error("Error fetching profile:", error);
      
      if (retryCount < maxRetries && mountedRef.current) {
        console.log(`🔄 Retrying profile fetch in 1 second (${retryCount + 1}/${maxRetries})`);
        setTimeout(() => {
          fetchProfile(userId, retryCount + 1);
        }, 1000);
      } else {
        setProfile(null);
      }
    } finally {
      if (mountedRef.current) {
        setProfileLoading(false);
      }
    }
  };

  // Main auth initialization effect
  useEffect(() => {
    if (initializedRef.current) return;

    const initializeAuth = async () => {
      try {
        console.log("🔄 Initializing auth...");
        
        const { data: { session }, error } = await supabase.auth.getSession();
        
        if (error) {
          console.error("❌ Error getting session:", error);
          if (mountedRef.current) {
            setUser(null);
            setProfile(null);
            setAuthLoading(false);
          }
          return;
        }

        console.log("📋 Session check:", session ? "Found session" : "No session");

        if (mountedRef.current) {
          setUser(session?.user ?? null);
          
          if (session?.user) {
            console.log("👤 User found, fetching profile...");
            fetchProfile(session.user.id);
          } else {
            setProfile(null);
          }
          
          setAuthLoading(false);
        }
      } catch (error) {
        console.error("💥 Unexpected error in initializeAuth:", error);
        if (mountedRef.current) {
          setUser(null);
          setProfile(null);
          setAuthLoading(false);
        }
      } finally {
        initializedRef.current = true;
      }
    };

    // Set up auth state listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (!mountedRef.current) return;

        console.log("🔄 Auth state change:", event, session?.user?.id || "no user");
        
        setUser(session?.user ?? null);
        
        if (session?.user) {
          console.log("👤 User authenticated, fetching profile...");
          fetchProfile(session.user.id);
        } else {
          console.log("👤 User signed out, clearing profile");
          setProfile(null);
        }
        
        // Only set auth loading to false after initial load
        if (initializedRef.current) {
          setAuthLoading(false);
        }
      }
    );

    initializeAuth();

    return () => {
      subscription.unsubscribe();
    };
  }, []); // No dependencies to prevent re-initialization

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  const signOut = async () => {
    try {
      console.log("🚪 Signing out...");
      setAuthLoading(true);
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      
      setUser(null);
      setProfile(null);
      initializedRef.current = false;
      console.log("✅ Signed out successfully");
    } catch (error) {
      console.error("Error signing out:", error);
    } finally {
      setAuthLoading(false);
    }
  };

  // Determine overall loading state
  const loading = authLoading || (user && !profile && profileLoading);

  const value = {
    user,
    profile,
    isAuthenticated: !!user,
    loading,
    hasProfile: !!profile,
    userRole: profile?.role || null,
    signOut,
  };

  console.log("🎯 Auth state:", {
    isAuthenticated: value.isAuthenticated,
    userRole: value.userRole,
    loading: value.loading,
    authLoading,
    profileLoading,
    hasProfile: value.hasProfile,
    initialized: initializedRef.current,
    userId: user?.id || "undefined"
  });

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
