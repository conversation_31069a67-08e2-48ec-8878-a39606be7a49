import { getDefaultConfig } from "expo/metro-config";
import { withNativeWind } from "nativewind/metro";

const config = getDefaultConfig(import.meta.url);

// Add web support
config.resolver.platforms = ["ios", "android", "native", "web"];

// Add web extensions
config.resolver.sourceExts.push("web.js", "web.jsx", "web.ts", "web.tsx");

export default withNativeWind(config, { input: "./src/index.css" });
