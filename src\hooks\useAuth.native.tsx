import React, {
  createContext,
  useState,
  useContext,
  useEffect,
  useRef,
} from "react";
import { supabase } from "@/integrations/supabase/client.native";
import { User } from "@supabase/supabase-js";
import AsyncStorage from "@react-native-async-storage/async-storage";

// Define the Profile type
export type Profile = {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  role: "admin" | "therapist" | "patient" | null;
  google_id?: string;
};

// Define the AuthContextType
type AuthContextType = {
  user: User | null;
  profile: Profile | null;
  isAuthenticated: boolean;
  loading: boolean;
  hasProfile: boolean;
  userRole: string | null;
  signOut: () => Promise<void>;
};

// Create the context with a default value
const AuthContext = createContext<AuthContextType>({
  user: null,
  profile: null,
  isAuthenticated: false,
  loading: true,
  hasProfile: false,
  userRole: null,
  signOut: async () => {},
});

// Custom hook to use the auth context
export const useAuth = () => useContext(AuthContext);

// Initialize Supabase with AsyncStorage for React Native
supabase.auth.setSession({
  access_token: "",
  refresh_token: "",
});

// Provider component to wrap the app
export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [authLoading, setAuthLoading] = useState(true);
  const [profileLoading, setProfileLoading] = useState(false);
  const mountedRef = useRef(true);
  const initializedRef = useRef(false);

  const fetchProfile = async (userId: string, retryCount = 0) => {
    const maxRetries = 3;

    try {
      console.log(
        `🔍 Fetching profile for user: ${userId} (attempt ${retryCount + 1})`
      );
      setProfileLoading(true);

      const { data, error } = await supabase
        .from("profiles")
        .select("id, first_name, last_name, email, role, google_id")
        .eq("id", userId)
        .single();

      if (error) {
        console.error("❌ Error fetching profile:", error);

        // Retry logic for profile fetch
        if (retryCount < maxRetries) {
          console.log(
            `🔄 Retrying profile fetch (${retryCount + 1}/${maxRetries})...`
          );
          setTimeout(() => {
            if (mountedRef.current) {
              fetchProfile(userId, retryCount + 1);
            }
          }, 1000 * (retryCount + 1)); // Exponential backoff
          return;
        }

        if (mountedRef.current) {
          setProfile(null);
          setProfileLoading(false);
        }
        return;
      }

      console.log(
        "✅ Profile fetched successfully:",
        data ? "Found profile" : "No profile"
      );

      if (mountedRef.current) {
        setProfile(data);
        setProfileLoading(false);
      }
    } catch (error) {
      console.error("❌ Unexpected error fetching profile:", error);
      if (mountedRef.current) {
        setProfile(null);
        setProfileLoading(false);
      }
    }
  };

  // Initialize auth state
  useEffect(() => {
    if (initializedRef.current) return;
    initializedRef.current = true;

    const initializeAuth = async () => {
      try {
        console.log("🔄 Initializing auth...");

        const {
          data: { session },
          error,
        } = await supabase.auth.getSession();

        if (error) {
          console.error("❌ Error getting session:", error);
          if (mountedRef.current) {
            setUser(null);
            setProfile(null);
            setAuthLoading(false);
          }
          return;
        }

        console.log(
          "📋 Session check:",
          session ? "Found session" : "No session"
        );

        if (mountedRef.current) {
          setUser(session?.user ?? null);

          if (session?.user) {
            console.log("👤 User found, fetching profile...");
            fetchProfile(session.user.id);
          } else {
            setProfile(null);
          }

          setAuthLoading(false);
        }
      } catch (error) {
        console.error("❌ Unexpected error initializing auth:", error);
        if (mountedRef.current) {
          setUser(null);
          setProfile(null);
          setAuthLoading(false);
        }
      }
    };

    // Set up auth state listener
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (!mountedRef.current) return;

      console.log(
        "🔄 Auth state change:",
        event,
        session?.user?.id || "no user"
      );

      setUser(session?.user ?? null);

      if (session?.user) {
        console.log("👤 User authenticated, fetching profile...");
        fetchProfile(session.user.id);
      } else {
        console.log("👤 User signed out, clearing profile");
        setProfile(null);
      }

      // Only set auth loading to false after initial load
      if (initializedRef.current) {
        setAuthLoading(false);
      }
    });

    initializeAuth();

    return () => {
      subscription.unsubscribe();
    };
  }, []); // No dependencies to prevent re-initialization

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  const signOut = async () => {
    try {
      console.log("🚪 Signing out...");
      setAuthLoading(true);
      const { error } = await supabase.auth.signOut();
      if (error) throw error;

      setUser(null);
      setProfile(null);
      initializedRef.current = false;
      console.log("✅ Signed out successfully");
    } catch (error) {
      console.error("Error signing out:", error);
    } finally {
      setAuthLoading(false);
    }
  };

  // Determine overall loading state
  const loading = authLoading || (user && !profile && profileLoading);

  const value = {
    user,
    profile,
    isAuthenticated: !!user,
    loading,
    hasProfile: !!profile,
    userRole: profile?.role || null,
    signOut,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}
