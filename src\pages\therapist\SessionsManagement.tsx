
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar, Clock, Plus, Search, ArrowLeft, MapPin, Edit, Play } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { Layout } from "@/components/Layout";
import { Skeleton } from "@/components/ui/skeleton";
import { DateTimeSlider } from "@/components/ui/datetime-slider";
import { useNavigate } from "react-router-dom";
import { format } from "date-fns";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/dialog";

type SessionType = "aba_therapy" | "speech_therapy" | "occupational_therapy" | "behavioral_therapy";
type SessionStatus = "scheduled" | "confirmed" | "cancelled" | "completed" | "rescheduled";

interface TherapySession {
  id: string;
  scheduled_datetime: string;
  duration_minutes: number;
  session_type: SessionType;
  status: SessionStatus;
  is_virtual: boolean;
  meeting_link: string | null;
  session_notes: string | null;
  focus_areas: string | null;
  patient: {
    id: string;
    guardian_name: string;
    user: {
      first_name: string;
      last_name: string;
    } | null;
  } | null;
}

interface Patient {
  id: string;
  guardian_name: string;
  user: {
    first_name: string;
    last_name: string;
  } | null;
}

export default function SessionsManagement() {
  const [sessions, setSessions] = useState<TherapySession[]>([]);
  const [patients, setPatients] = useState<Patient[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingSession, setEditingSession] = useState<TherapySession | null>(null);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [newSession, setNewSession] = useState({
    patient_id: "",
    scheduled_datetime: new Date(),
    duration_minutes: 45,
    session_type: "aba_therapy" as SessionType,
    is_virtual: false,
    meeting_link: "",
    focus_areas: "",
  });
  const { profile } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  useEffect(() => {
    if (profile?.id) {
      fetchSessions();
      fetchPatients();
    }
  }, [profile]);

  const fetchSessions = async () => {
    if (!profile?.id) {
      console.log("No profile ID available");
      setLoading(false);
      return;
    }

    try {
      console.log("Fetching sessions for therapist:", profile.id);
      const { data, error } = await supabase
        .from("therapy_sessions")
        .select(`
          *,
          patient:patients(
            id,
            guardian_name,
            user:profiles!patients_user_id_fkey(first_name, last_name)
          )
        `)
        .eq("therapist_id", profile.id)
        .order("scheduled_datetime", { ascending: true });

      if (error) {
        console.error("Error fetching sessions:", error);
        throw error;
      }
      
      console.log("Sessions fetched successfully:", data);
      setSessions((data as any) || []);
    } catch (error: any) {
      console.error("Sessions fetch error:", error);
      toast({
        title: "Error",
        description: "Failed to fetch sessions",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchPatients = async () => {
    if (!profile?.id) {
      console.log("No profile ID available for patients");
      return;
    }

    try {
      console.log("Fetching patients for therapist:", profile.id);
      const { data, error } = await supabase
        .from("patients")
        .select(`
          id,
          guardian_name,
          user:profiles!patients_user_id_fkey(first_name, last_name)
        `)
        .eq("assigned_therapist_id", profile.id);

      if (error) {
        console.error("Error fetching patients:", error);
        throw error;
      }
      
      console.log("Patients fetched successfully:", data);
      setPatients((data as any) || []);
    } catch (error: any) {
      console.error("Patients fetch error:", error);
      toast({
        title: "Error",
        description: "Failed to fetch patients",
        variant: "destructive",
      });
    }
  };

  const createSession = async () => {
    if (!profile?.id) {
      toast({
        title: "Error",
        description: "User profile not loaded",
        variant: "destructive",
      });
      return;
    }

    try {
      console.log("Creating session with data:", {
        patient_id: newSession.patient_id,
        scheduled_datetime: newSession.scheduled_datetime.toISOString(),
        duration_minutes: newSession.duration_minutes,
        session_type: newSession.session_type,
        is_virtual: newSession.is_virtual,
        meeting_link: newSession.meeting_link || null,
        focus_areas: newSession.focus_areas || null,
        therapist_id: profile.id,
      });

      const { error } = await supabase
        .from("therapy_sessions")
        .insert({
          patient_id: newSession.patient_id,
          scheduled_datetime: newSession.scheduled_datetime.toISOString(),
          duration_minutes: newSession.duration_minutes,
          session_type: newSession.session_type,
          is_virtual: newSession.is_virtual,
          meeting_link: newSession.meeting_link || null,
          focus_areas: newSession.focus_areas || null,
          therapist_id: profile.id,
        });

      if (error) {
        console.error("Error creating session:", error);
        throw error;
      }

      toast({
        title: "Success",
        description: "Session created successfully",
      });

      setShowCreateForm(false);
      setNewSession({
        patient_id: "",
        scheduled_datetime: new Date(),
        duration_minutes: 45,
        session_type: "aba_therapy",
        is_virtual: false,
        meeting_link: "",
        focus_areas: "",
      });
      fetchSessions();
    } catch (error: any) {
      console.error("Create session error:", error);
      toast({
        title: "Error",
        description: "Failed to create session",
        variant: "destructive",
      });
    }
  };

  const updateSession = async () => {
    if (!editingSession) return;

    try {
      console.log("Updating session with data:", {
        scheduled_datetime: editingSession.scheduled_datetime,
        duration_minutes: editingSession.duration_minutes,
        session_type: editingSession.session_type,
        is_virtual: editingSession.is_virtual,
        meeting_link: editingSession.meeting_link,
        focus_areas: editingSession.focus_areas,
        session_notes: editingSession.session_notes,
      });

      const { error } = await supabase
        .from("therapy_sessions")
        .update({
          scheduled_datetime: editingSession.scheduled_datetime,
          duration_minutes: editingSession.duration_minutes,
          session_type: editingSession.session_type,
          is_virtual: editingSession.is_virtual,
          meeting_link: editingSession.meeting_link,
          focus_areas: editingSession.focus_areas,
          session_notes: editingSession.session_notes,
        })
        .eq("id", editingSession.id);

      if (error) {
        console.error("Error updating session:", error);
        throw error;
      }

      toast({
        title: "Success",
        description: "Session updated successfully",
      });

      setShowEditDialog(false);
      setEditingSession(null);
      fetchSessions();
    } catch (error: any) {
      console.error("Update session error:", error);
      toast({
        title: "Error",
        description: "Failed to update session",
        variant: "destructive",
      });
    }
  };

  const startSession = async (sessionId: string) => {
    try {
      console.log("Starting session:", sessionId);
      const { error } = await supabase
        .from("therapy_sessions")
        .update({ status: "confirmed" as SessionStatus })
        .eq("id", sessionId);

      if (error) {
        console.error("Error starting session:", error);
        throw error;
      }

      toast({
        title: "Success",
        description: "Session started successfully",
      });

      fetchSessions();
    } catch (error: any) {
      console.error("Start session error:", error);
      toast({
        title: "Error",
        description: "Failed to start session",
        variant: "destructive",
      });
    }
  };

  const getPatientName = (session: TherapySession) => {
    if (session.patient?.user?.first_name && session.patient?.user?.last_name) {
      return `${session.patient.user.first_name} ${session.patient.user.last_name}`;
    }
    if (session.patient?.guardian_name) {
      return session.patient.guardian_name;
    }
    return "Unknown Patient";
  };

  const filteredSessions = sessions.filter(session => {
    const patientName = getPatientName(session);
    return patientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
           session.session_type?.toLowerCase().includes(searchTerm.toLowerCase());
  });

  if (loading) {
    return (
      <Layout>
        <div className="min-h-screen bg-gray-50 p-6">
          <div className="max-w-4xl mx-auto space-y-6">
            <Skeleton className="h-12 w-64" />
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <Skeleton key={i} className="h-32 rounded-2xl" />
              ))}
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white px-6 py-6 flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate("/dashboard")}
            className="p-2"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-2xl font-bold text-gray-900">Sessions Management</h1>
        </div>

        <div className="max-w-4xl mx-auto px-6 space-y-6">
          {/* Search and Actions */}
          <div className="flex items-center justify-between gap-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search sessions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 h-12 rounded-xl border-gray-200"
              />
            </div>
            <Button 
              onClick={() => setShowCreateForm(!showCreateForm)}
              className="bg-app-purple hover:bg-app-purple/90 text-white rounded-xl h-12 px-6"
            >
              <Plus className="h-4 w-4 mr-2" />
              Schedule Session
            </Button>
          </div>

          {/* Create Session Form */}
          {showCreateForm && (
            <Card className="bg-white border-0 shadow-lg rounded-2xl">
              <CardHeader>
                <CardTitle className="text-app-purple">Schedule New Session</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Select value={newSession.patient_id} onValueChange={(value) => setNewSession({...newSession, patient_id: value})}>
                  <SelectTrigger className="rounded-xl">
                    <SelectValue placeholder="Select patient" />
                  </SelectTrigger>
                  <SelectContent>
                    {patients.map((patient) => (
                      <SelectItem key={patient.id} value={patient.id}>
                        {patient.user?.first_name ? `${patient.user.first_name} ${patient.user.last_name}` : patient.guardian_name || 'Unknown Patient'}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <DateTimeSlider
                  value={newSession.scheduled_datetime}
                  onChange={(date) => setNewSession({...newSession, scheduled_datetime: date})}
                  placeholder="Select date and time"
                  className="rounded-xl"
                />

                <Select value={newSession.session_type} onValueChange={(value: SessionType) => setNewSession({...newSession, session_type: value})}>
                  <SelectTrigger className="rounded-xl">
                    <SelectValue placeholder="Select session type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="aba_therapy">ABA Therapy</SelectItem>
                    <SelectItem value="speech_therapy">Speech Therapy</SelectItem>
                    <SelectItem value="occupational_therapy">Occupational Therapy</SelectItem>
                    <SelectItem value="behavioral_therapy">Behavioral Therapy</SelectItem>
                  </SelectContent>
                </Select>

                <Input 
                  type="number" 
                  placeholder="Duration (minutes)" 
                  value={newSession.duration_minutes}
                  onChange={(e) => setNewSession({...newSession, duration_minutes: parseInt(e.target.value)})}
                  className="rounded-xl" 
                />

                <Textarea 
                  placeholder="Focus areas" 
                  value={newSession.focus_areas}
                  onChange={(e) => setNewSession({...newSession, focus_areas: e.target.value})}
                  className="rounded-xl" 
                />

                <div className="flex items-center space-x-2">
                  <input 
                    type="checkbox" 
                    id="is_virtual"
                    checked={newSession.is_virtual}
                    onChange={(e) => setNewSession({...newSession, is_virtual: e.target.checked})}
                  />
                  <label htmlFor="is_virtual">Virtual session</label>
                </div>

                {newSession.is_virtual && (
                  <Input 
                    placeholder="Meeting link" 
                    value={newSession.meeting_link}
                    onChange={(e) => setNewSession({...newSession, meeting_link: e.target.value})}
                    className="rounded-xl" 
                  />
                )}

                <div className="flex space-x-2">
                  <Button 
                    variant="outline" 
                    onClick={() => setShowCreateForm(false)}
                    className="rounded-xl"
                  >
                    Cancel
                  </Button>
                  <Button 
                    onClick={createSession}
                    className="bg-app-purple hover:bg-app-purple/90 text-white rounded-xl"
                    disabled={!newSession.patient_id}
                  >
                    Schedule Session
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Sessions List */}
          <div className="space-y-4">
            {sessions.length === 0 ? (
              <Card className="bg-white border-0 shadow-lg rounded-2xl">
                <CardContent className="p-12 text-center">
                  <Calendar className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">No Sessions Scheduled</h3>
                  <p className="text-gray-600">Start by scheduling therapy sessions for your patients.</p>
                </CardContent>
              </Card>
            ) : (
              filteredSessions.map((session) => (
                <Card key={session.id} className="bg-white border-0 shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300">
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div className="space-y-3">
                        <div className="flex items-center space-x-3">
                          <CardTitle className="text-lg text-gray-900">
                            {getPatientName(session)}
                          </CardTitle>
                          {!session.is_virtual && <MapPin className="h-4 w-4 text-green-500" />}
                        </div>
                        <div className="flex items-center space-x-3">
                          <Badge className="bg-app-purple text-white">
                            {session.session_type.replace('_', ' ').toUpperCase()}
                          </Badge>
                          <Badge 
                            className={`${
                              session.status === 'completed' ? 'bg-green-500 text-white' :
                              session.status === 'confirmed' ? 'bg-blue-500 text-white' :
                              session.status === 'cancelled' ? 'bg-red-500 text-white' :
                              session.status === 'rescheduled' ? 'bg-orange-500 text-white' :
                              'bg-yellow-500 text-white'
                            }`}
                          >
                            {session.status}
                          </Badge>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="flex items-center text-gray-600 mb-1">
                          <Clock className="h-4 w-4 mr-1" />
                          <span className="text-sm">{session.duration_minutes} min</span>
                        </div>
                        <div className="text-sm font-semibold text-gray-900">
                          {format(new Date(session.scheduled_datetime), "MMM d, yyyy")}
                        </div>
                        <div className="text-sm text-gray-600">
                          {format(new Date(session.scheduled_datetime), "h:mm a")}
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {session.focus_areas && (
                      <div>
                        <p className="text-sm text-gray-600 mb-1">Focus Areas:</p>
                        <p className="text-gray-900">{session.focus_areas}</p>
                      </div>
                    )}
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-600 mb-1">Type:</p>
                        <p className="font-semibold text-gray-900">
                          {session.is_virtual ? 'Virtual Session' : 'In-Person Session'}
                        </p>
                      </div>
                      <div className="flex space-x-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          className="rounded-xl border-app-purple text-app-purple hover:bg-app-purple hover:text-white"
                          onClick={() => {
                            setEditingSession(session);
                            setShowEditDialog(true);
                          }}
                        >
                          <Edit className="h-4 w-4 mr-1" />
                          Edit
                        </Button>
                        
                        <Button 
                          size="sm"
                          className="bg-app-purple hover:bg-app-purple/90 text-white rounded-xl"
                          onClick={() => startSession(session.id)}
                          disabled={session.status === 'confirmed' || session.status === 'completed'}
                        >
                          <Play className="h-4 w-4 mr-1" />
                          {session.status === 'confirmed' ? 'In Progress' : 'Start Session'}
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>

          {/* Edit Session Dialog */}
          {editingSession && (
            <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
              <DialogContent className="max-w-lg">
                <DialogHeader>
                  <DialogTitle>Edit Session</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Session Type
                    </label>
                    <Select 
                      value={editingSession.session_type} 
                      onValueChange={(value: SessionType) => setEditingSession({...editingSession, session_type: value})}
                    >
                      <SelectTrigger className="rounded-xl">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="aba_therapy">ABA Therapy</SelectItem>
                        <SelectItem value="speech_therapy">Speech Therapy</SelectItem>
                        <SelectItem value="occupational_therapy">Occupational Therapy</SelectItem>
                        <SelectItem value="behavioral_therapy">Behavioral Therapy</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Duration (minutes)
                    </label>
                    <Input
                      type="number"
                      value={editingSession.duration_minutes}
                      onChange={(e) => setEditingSession({...editingSession, duration_minutes: parseInt(e.target.value)})}
                      className="rounded-xl"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Focus Areas
                    </label>
                    <Textarea
                      value={editingSession.focus_areas || ''}
                      onChange={(e) => setEditingSession({...editingSession, focus_areas: e.target.value})}
                      className="rounded-xl"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Session Notes
                    </label>
                    <Textarea
                      value={editingSession.session_notes || ''}
                      onChange={(e) => setEditingSession({...editingSession, session_notes: e.target.value})}
                      className="rounded-xl"
                    />
                  </div>

                  <div className="flex items-center space-x-2">
                    <input 
                      type="checkbox" 
                      id="edit_is_virtual"
                      checked={editingSession.is_virtual}
                      onChange={(e) => setEditingSession({...editingSession, is_virtual: e.target.checked})}
                    />
                    <label htmlFor="edit_is_virtual">Virtual session</label>
                  </div>

                  {editingSession.is_virtual && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Meeting Link
                      </label>
                      <Input
                        value={editingSession.meeting_link || ''}
                        onChange={(e) => setEditingSession({...editingSession, meeting_link: e.target.value})}
                        className="rounded-xl"
                      />
                    </div>
                  )}

                  <div className="flex space-x-2 pt-4">
                    <Button 
                      variant="outline" 
                      onClick={() => setShowEditDialog(false)}
                      className="rounded-xl"
                    >
                      Cancel
                    </Button>
                    <Button 
                      onClick={updateSession}
                      className="bg-app-purple hover:bg-app-purple/90 text-white rounded-xl"
                    >
                      Update Session
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          )}

          {/* Bottom spacing */}
          <div className="h-8"></div>
        </div>
      </div>
    </Layout>
  );
}
