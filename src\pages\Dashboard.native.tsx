import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, SafeAreaView, ScrollView } from 'react-native';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/integrations/supabase/client';

interface DashboardStats {
  totalGoals: number;
  completedGoals: number;
  totalActivities: number;
  completedTodayActivities: number;
  totalMilestones: number;
  upcomingSessions: number;
}

export default function DashboardNative() {
  const { profile } = useAuth();
  const [stats, setStats] = useState<DashboardStats>({
    totalGoals: 0,
    completedGoals: 0,
    totalActivities: 0,
    completedTodayActivities: 0,
    totalMilestones: 0,
    upcomingSessions: 0,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (profile?.id) {
      fetchDashboardStats();
    }
  }, [profile]);

  const fetchDashboardStats = async () => {
    try {
      // Get patient record
      let { data: patientData, error: patientError } = await supabase
        .from("patients")
        .select("id, assigned_therapist_id, autism_level")
        .eq("user_id", profile?.id)
        .maybeSingle();

      if (patientError) {
        console.error("Error fetching patient:", patientError);
        setLoading(false);
        return;
      }

      if (!patientData) {
        // Create new patient record
        const { data: newPatient, error: createError } = await supabase
          .from("patients")
          .insert({
            user_id: profile?.id,
            guardian_name: `${profile?.first_name || "Patient"} ${profile?.last_name || "User"}`,
            date_of_birth: new Date().toISOString().split('T')[0],
            autism_level: "1" as const
          })
          .select("id, assigned_therapist_id, autism_level")
          .single();

        if (createError) {
          console.error("Error creating patient:", createError);
          setLoading(false);
          return;
        }
        patientData = newPatient;
      }

      // Fetch goals stats
      const { data: goalsData } = await supabase
        .from("therapy_goals")
        .select("status")
        .eq("patient_id", patientData.id);

      // Fetch activities stats
      const { data: activitiesData } = await supabase
        .from("daily_activities")
        .select("id")
        .eq("patient_id", patientData.id)
        .eq("is_active", true);

      // Fetch today's completed activities
      const today = new Date().toISOString().split('T')[0];
      const { data: completionsData } = await supabase
        .from("activity_completions")
        .select("id")
        .eq("patient_id", patientData.id)
        .eq("completion_date", today)
        .eq("completed", true);

      // Fetch milestones stats
      const { data: milestonesData } = await supabase
        .from("milestones")
        .select("status")
        .eq("patient_id", patientData.id);

      // Fetch upcoming sessions
      const { data: sessionsData } = await supabase
        .from("therapy_sessions")
        .select("id")
        .eq("patient_id", patientData.id)
        .eq("status", "scheduled")
        .gte("scheduled_datetime", new Date().toISOString());

      setStats({
        totalGoals: goalsData?.length || 0,
        completedGoals: goalsData?.filter(g => g.status === 'completed').length || 0,
        totalActivities: activitiesData?.length || 0,
        completedTodayActivities: completionsData?.length || 0,
        totalMilestones: milestonesData?.filter(m => m.status === 'completed').length || 0,
        upcomingSessions: sessionsData?.length || 0,
      });
    } catch (error) {
      console.error("Error fetching dashboard stats:", error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <View style={styles.header}>
          <Text style={styles.welcomeText}>
            Welcome back, {profile?.first_name || 'Patient'}!
          </Text>
          <Text style={styles.subtitle}>Your Progress Dashboard</Text>
        </View>

        <View style={styles.statsContainer}>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{stats.completedGoals}/{stats.totalGoals}</Text>
            <Text style={styles.statLabel}>Goals Completed</Text>
          </View>

          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{stats.completedTodayActivities}/{stats.totalActivities}</Text>
            <Text style={styles.statLabel}>Today's Activities</Text>
          </View>

          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{stats.totalMilestones}</Text>
            <Text style={styles.statLabel}>Milestones Achieved</Text>
          </View>

          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{stats.upcomingSessions}</Text>
            <Text style={styles.statLabel}>Upcoming Sessions</Text>
          </View>
        </View>

        <View style={styles.navigationContainer}>
          <TouchableOpacity style={[styles.navButton, { backgroundColor: '#8B5CF6' }]}>
            <Text style={styles.navButtonText}>My Goals</Text>
          </TouchableOpacity>

          <TouchableOpacity style={[styles.navButton, { backgroundColor: '#10B981' }]}>
            <Text style={styles.navButtonText}>Activities</Text>
          </TouchableOpacity>

          <TouchableOpacity style={[styles.navButton, { backgroundColor: '#F59E0B' }]}>
            <Text style={styles.navButtonText}>Milestones</Text>
          </TouchableOpacity>

          <TouchableOpacity style={[styles.navButton, { backgroundColor: '#3B82F6' }]}>
            <Text style={styles.navButtonText}>Sessions</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 18,
    color: '#6B7280',
  },
  header: {
    padding: 20,
    alignItems: 'center',
    backgroundColor: 'white',
    marginBottom: 20,
  },
  welcomeText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 20,
    gap: 12,
    marginBottom: 20,
  },
  statCard: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    width: '48%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#6B7280',
    textAlign: 'center',
  },
  navigationContainer: {
    paddingHorizontal: 20,
    gap: 12,
  },
  navButton: {
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  navButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: 'white',
  },
});
