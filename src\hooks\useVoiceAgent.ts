import { useState, useRef, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { toast } from 'sonner';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/integrations/supabase/client';

export interface VoiceCommand {
  action: string;
  target?: string;
  parameters?: Record<string, any>;
  response?: string;
  confidence?: string;
  clarification_needed?: string;
  error?: string;
}

export const useVoiceAgent = () => {
  const [isListening, setIsListening] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [lastCommand, setLastCommand] = useState('');
  const [lastParsedCommand, setLastParsedCommand] = useState<VoiceCommand | null>(null);
  
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const navigate = useNavigate();
  const location = useLocation();
  const { profile } = useAuth();

  const startListening = useCallback(async () => {
    try {
      // CRITICAL: Proper audio constraints for better recognition
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          sampleRate: 16000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true // Add this for better audio quality
        }
      });

      // CRITICAL: Use WAV format for ElevenLabs compatibility
      const options: MediaRecorderOptions = {
        mimeType: 'audio/wav'
      };

      // Fallback to webm if wav is not supported
      if (!MediaRecorder.isTypeSupported('audio/wav')) {
        console.log('WAV not supported, falling back to webm');
        options.mimeType = 'audio/webm;codecs=opus';
      }

      const mediaRecorder = new MediaRecorder(stream, options);
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(audioChunksRef.current, { 
          type: mediaRecorder.mimeType 
        });
        await processVoiceCommand(audioBlob);
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorder.start(1000); // IMPORTANT: Collect data every second
      setIsListening(true);
      
      toast.info("🎤 Listening... Speak your command");

    } catch (error) {
      console.error('Error starting voice recording:', error);
      toast.error("Failed to access microphone. Please check permissions.");
    }
  }, []);

  const stopListening = useCallback(() => {
    if (mediaRecorderRef.current && isListening) {
      mediaRecorderRef.current.stop();
      setIsListening(false);
    }
  }, [isListening]);

  const processVoiceCommand = async (audioBlob: Blob) => {
    setIsProcessing(true);
    
    try {
      // CRITICAL: Validate audio size (common cause of "speech not recognized")
      if (audioBlob.size < 1000) {
        throw new Error('Audio recording too short. Please try speaking for longer.');
      }

      console.log('Processing audio blob:', audioBlob.size, 'bytes');

      // Convert audio to base64
      const reader = new FileReader();
      const base64Audio = await new Promise<string>((resolve, reject) => {
        reader.onload = () => {
          const result = reader.result as string;
          const base64 = result.split(',')[1]; // Remove data:audio/wav;base64, prefix
          resolve(base64);
        };
        reader.onerror = () => reject(new Error('Failed to convert audio to base64'));
        reader.readAsDataURL(audioBlob);
      });

      // Call voice-to-text function with proper error handling
      const speechResp = await supabase.functions.invoke('voice-to-text', {
        body: { audioData: base64Audio }
      });

      // Check for Supabase function errors
      if (speechResp.error) {
        console.error('Supabase function error:', speechResp.error);
        throw new Error(speechResp.error.message || 'Failed to process speech');
      }

      // Validate response data
      if (!speechResp.data) {
        throw new Error('No response data from speech service');
      }

      // Check for API errors in the response
      if (speechResp.data.error) {
        throw new Error(speechResp.data.error);
      }

      const transcript = speechResp.data.transcript || '';
      setLastCommand(transcript);

      if (!transcript.trim()) {
        toast.warning("No speech detected. Please try speaking more clearly and for a longer duration.");
        return;
      }

      console.log('Transcript received:', transcript);

      // Build context for command processing
      const context = {
        path: location.pathname,
        role: profile?.role,
        timestamp: new Date().toISOString()
      };

      // Process the command with improved error handling
      const commandResp = await supabase.functions.invoke('therapist-command-parser', {
        body: { 
          transcript: transcript.trim(),
          context
        }
      });

      console.log('Command parser response:', commandResp);

      // Handle command processing response
      let parsedCommand: VoiceCommand;

      if (commandResp.error) {
        console.error('Command processing error:', commandResp.error);
        // Create fallback command
        parsedCommand = {
          action: 'help',
          response: 'I had trouble processing that command. Please try rephrasing.',
          confidence: 'low',
          error: commandResp.error.message
        };
      } else if (!commandResp.data) {
        console.error('No command data received');
        parsedCommand = {
          action: 'help',
          response: 'I couldn\'t understand that command. Please try again.',
          confidence: 'low'
        };
      } else {
        parsedCommand = commandResp.data;
      }

      setLastParsedCommand(parsedCommand);

      // Execute the command even if there was an error (fallback commands)
      await executeCommand(parsedCommand);

    } catch (error) {
      console.error('Error processing voice command:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to process command';
      toast.error(errorMessage);
      
      // Set a fallback command for display
      setLastParsedCommand({
        action: 'error',
        response: 'Voice processing failed. Please try again.',
        confidence: 'low',
        error: errorMessage
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const executeCommand = async (command: VoiceCommand) => {
    console.log('Executing command:', command);

    try {
      switch (command.action) {
        case 'view_patients':
          navigate('/therapist/patients');
          toast.success('Navigating to patients list');
          break;

        case 'view_goals':
          navigate('/therapist/goals');
          toast.success('Navigating to goals management');
          break;

        case 'view_activities':
          navigate('/therapist/activities');
          toast.success('Navigating to activities');
          break;

        case 'view_sessions':
          navigate('/therapist/sessions');
          toast.success('Navigating to sessions');
          break;

        case 'schedule_session':
          navigate('/therapist/sessions');
          if (command.parameters?.patient_name) {
            toast.info(`Ready to schedule session for ${command.parameters.patient_name}`);
          } else {
            toast.info('Ready to schedule new session');
          }
          break;

        case 'view_patient':
          if (command.parameters?.patient_name) {
            navigate('/therapist/patients');
            toast.info(`Looking for patient: ${command.parameters.patient_name}`);
          } else {
            navigate('/therapist/patients');
            toast.warning('Patient name not specified. Showing all patients.');
          }
          break;

        case 'create_goal':
          navigate('/therapist/goals');
          if (command.parameters?.patient_name) {
            toast.info(`Ready to create goal for: ${command.parameters.patient_name}`);
          } else {
            toast.info('Ready to create new goal');
          }
          break;

        case 'update_progress':
          if (command.parameters?.progress_percentage) {
            const percentage = command.parameters.progress_percentage;
            if (command.parameters?.patient_name) {
              toast.success(`Updated ${command.parameters.patient_name}'s progress to ${percentage}%`);
            } else {
              toast.info(`Ready to update progress to ${percentage}%`);
            }
          } else {
            toast.warning('Progress percentage not specified');
          }
          break;

        case 'generate_report':
          if (command.parameters?.patient_name) {
            toast.info(`Generating report for: ${command.parameters.patient_name}`);
            setTimeout(() => {
              toast.success(`Report generated for ${command.parameters.patient_name}`);
            }, 2000);
          } else {
            toast.warning('Patient name required for report generation');
          }
          break;

        case 'navigate':
          if (command.target) {
            navigate(command.target);
            toast.success(`Navigating to ${command.target}`);
          }
          break;

        case 'sign_out':
          toast.info("Logging out...");
          window.location.href = "/auth";
          break;

        case 'help':
          toast.info('Voice commands: view patients, create goals, schedule sessions, update progress, generate reports', {
            duration: 5000
          });
          break;

        case 'error':
          // Don't show additional error toast, just display the error response
          break;

        default:
          toast.warning(`Unknown command: ${command.action}`);
      }

      // Show confidence indicator
      if (command.confidence === 'low') {
        toast.warning('Low confidence in command interpretation. Please verify the action is correct.');
      }

      // Handle clarification requests
      if (command.clarification_needed) {
        toast.info(command.clarification_needed, { duration: 6000 });
      }

      // Speak response if available and no error
      if (command.response && !command.error) {
        await speakResponse(command.response);
      }

    } catch (error) {
      console.error('Error executing command:', error);
      toast.error("I encountered an error while executing that command.");
    }
  };

  const speakResponse = async (text: string) => {
    if (!text.trim()) return;

    setIsSpeaking(true);
    
    try {
      const response = await supabase.functions.invoke('text-to-speech', {
        body: { 
          text: text,
          voiceId: 'EXAVITQu4vr4xnSDxMaL' // Sarah voice
        }
      });

      // Check for Supabase function errors
      if (response.error) {
        console.log('TTS error, but continuing:', response.error);
        setIsSpeaking(false);
        return;
      }

      // Validate response data
      if (!response.data) {
        console.log('No TTS response data, but continuing');
        setIsSpeaking(false);
        return;
      }

      // Check for API errors in the TTS response
      if (response.data.error) {
        console.log('TTS API error, but continuing:', response.data.error);
        setIsSpeaking(false);
        return;
      }

      if (response.data.audioContent) {
        const audioBlob = new Blob(
          [Uint8Array.from(atob(response.data.audioContent), c => c.charCodeAt(0))],
          { type: 'audio/mpeg' }
        );
        
        const audioUrl = URL.createObjectURL(audioBlob);
        
        if (audioRef.current) {
          audioRef.current.pause();
        }
        
        audioRef.current = new Audio(audioUrl);
        audioRef.current.onended = () => {
          setIsSpeaking(false);
          URL.revokeObjectURL(audioUrl);
        };
        
        await audioRef.current.play();
      }

    } catch (error) {
      console.log('TTS failed, but continuing:', error);
      setIsSpeaking(false);
    }
  };

  const stopSpeaking = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      setIsSpeaking(false);
    }
  };

  return {
    isListening,
    isProcessing,
    isSpeaking,
    lastCommand,
    lastParsedCommand,
    startListening,
    stopListening,
    speakResponse,
    stopSpeaking
  };
};
