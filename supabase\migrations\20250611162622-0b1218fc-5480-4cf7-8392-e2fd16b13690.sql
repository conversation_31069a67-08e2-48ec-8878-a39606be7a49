
-- Ultimate fix: Drop everything with <PERSON><PERSON><PERSON><PERSON> to remove all dependencies
-- This will completely clean up all conflicting policies and functions

-- Step 1: Drop the problematic function with <PERSON><PERSON><PERSON><PERSON> to remove all dependencies
DROP FUNCTION IF EXISTS public.get_user_role() CASCADE;
DROP FUNCTION IF EXISTS public.get_current_user_role() CASCADE;
DROP FUNCTION IF EXISTS public.get_user_role_safe() CASCADE;
DROP FUNCTION IF EXISTS public.get_current_user_role_safe() CASCADE;

-- Step 2: Drop any remaining policies on all tables to start completely fresh
DROP POLICY IF EXISTS "Therapists can insert patients" ON public.patients;
DROP POLICY IF EXISTS "Admins can manage all patients" ON public.patients;

-- Drop all remaining policies on profiles
DO $$ 
DECLARE 
    pol RECORD;
BEGIN
    FOR pol IN SELECT policyname FROM pg_policies WHERE tablename = 'profiles' AND schemaname = 'public'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || pol.policyname || '" ON public.profiles';
    END LOOP;
END $$;

-- Drop all remaining policies on patients
DO $$ 
DECLARE 
    pol RECORD;
BEGIN
    FOR pol IN SELECT policyname FROM pg_policies WHERE tablename = 'patients' AND schemaname = 'public'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || pol.policyname || '" ON public.patients';
    END LOOP;
END $$;

-- Step 3: Create the safe security definer function
CREATE OR REPLACE FUNCTION public.get_safe_user_role()
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
STABLE
AS $$
DECLARE
  user_role TEXT := 'patient';
BEGIN
  -- Temporarily disable RLS to prevent recursion
  PERFORM set_config('row_security', 'off', true);
  
  -- Get the role directly without any RLS interference
  SELECT role INTO user_role 
  FROM public.profiles 
  WHERE id = auth.uid()
  LIMIT 1;
  
  -- Re-enable RLS
  PERFORM set_config('row_security', 'on', true);
  
  -- Return the role or default to patient
  RETURN COALESCE(user_role, 'patient');
  
EXCEPTION
  WHEN OTHERS THEN
    -- Ensure RLS is re-enabled even on error
    PERFORM set_config('row_security', 'on', true);
    RETURN 'patient';
END;
$$;

-- Step 4: Create clean, simple RLS policies for profiles
CREATE POLICY "profile_select_own" 
ON public.profiles 
FOR SELECT 
TO authenticated
USING (auth.uid() = id);

CREATE POLICY "profile_update_own" 
ON public.profiles 
FOR UPDATE 
TO authenticated
USING (auth.uid() = id);

CREATE POLICY "profile_insert_own" 
ON public.profiles 
FOR INSERT 
TO authenticated
WITH CHECK (auth.uid() = id);

CREATE POLICY "profile_admin_all" 
ON public.profiles 
FOR ALL 
TO authenticated
USING (public.get_safe_user_role() = 'admin')
WITH CHECK (public.get_safe_user_role() = 'admin');

-- Step 5: Create clean policies for patients table
CREATE POLICY "patients_owner_access" 
ON public.patients 
FOR ALL 
TO authenticated
USING (user_id = auth.uid())
WITH CHECK (user_id = auth.uid());

CREATE POLICY "patients_admin_access" 
ON public.patients 
FOR ALL 
TO authenticated
USING (public.get_safe_user_role() = 'admin')
WITH CHECK (public.get_safe_user_role() = 'admin');

-- Step 6: Create policies for therapy types
CREATE POLICY "therapy_types_read_all" 
ON public.therapy_types 
FOR SELECT 
TO authenticated
USING (true);

CREATE POLICY "therapy_types_admin_manage" 
ON public.therapy_types 
FOR ALL 
TO authenticated
USING (public.get_safe_user_role() = 'admin')
WITH CHECK (public.get_safe_user_role() = 'admin');

-- Ensure RLS is enabled
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.patients ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.therapy_types ENABLE ROW LEVEL SECURITY;
