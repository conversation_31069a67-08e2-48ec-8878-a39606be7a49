
import React, { useEffect, useRef, useState } from "react";
import { Target, TrendingUp } from "lucide-react";
import { Carousel, CarouselContent, CarouselItem } from "@/components/ui/carousel";

interface PatientDashboardCarouselProps {
  autismLevel: string | undefined;
  completedGoals: number;
  totalGoals: number;
  totalMilestones: number;
}

export function PatientDashboardCarousel({
  autismLevel,
  completedGoals,
  totalGoals,
  totalMilestones
}: PatientDashboardCarouselProps) {
  // Carousel auto-slide
  const [carouselApi, setCarouselApi] = useState<any>(null);
  const autoSlideIntervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (!carouselApi) return;
    if (autoSlideIntervalRef.current) {
      clearInterval(autoSlideIntervalRef.current);
    }
    autoSlideIntervalRef.current = setInterval(() => {
      if (carouselApi && typeof carouselApi.scrollNext === "function") {
        if (!carouselApi.canScrollNext()) {
          carouselApi.scrollTo(0);
        } else {
          carouselApi.scrollNext();
        }
      }
    }, 3500);
    return () => {
      if (autoSlideIntervalRef.current) {
        clearInterval(autoSlideIntervalRef.current);
      }
    };
  }, [carouselApi]);

  return (
    <div className="bg-white border-0 shadow-sm hover:shadow-md transition-all duration-300 rounded-2xl p-4 md:p-6 flex flex-col items-center">
      <Carousel className="w-full max-w-lg" opts={{ loop: false }} orientation="horizontal" setApi={setCarouselApi}>
        <CarouselContent>
          {/* 1. Autism Level */}
          <CarouselItem className="flex flex-col items-center justify-center">
            <div className="w-10 h-10 md:w-16 md:h-16 bg-app-purple/10 rounded-full flex items-center justify-center mx-auto mb-2 md:mb-4">
              <Target className="h-5 w-5 md:h-8 md:w-8 text-app-purple" />
            </div>
            <h3 className="text-gray-600 text-xs md:text-sm font-medium mb-1">Autism Level</h3>
            <div className="text-lg font-bold text-gray-900">
              {(() => {
                const raw = autismLevel || "1";
                if (raw === "1") return "Level 1";
                if (raw === "2") return "Level 2";
                if (raw === "3") return "Level 3";
                return raw;
              })()}
            </div>
          </CarouselItem>
          {/* 2. Completed Goals */}
          <CarouselItem className="flex flex-col items-center justify-center">
            <div className="w-10 h-10 md:w-16 md:h-16 bg-app-yellow/10 rounded-full flex items-center justify-center mx-auto mb-2 md:mb-4">
              <Target className="h-5 w-5 md:h-8 md:w-8 text-app-yellow" />
            </div>
            <h3 className="text-gray-600 text-xs md:text-sm font-medium mb-1">Completed Goals</h3>
            <span className="text-lg font-bold text-gray-900">
              {completedGoals}/{totalGoals}
            </span>
          </CarouselItem>
          {/* 3. Milestones */}
          <CarouselItem className="flex flex-col items-center justify-center">
            <div className="w-10 h-10 md:w-16 md:h-16 bg-app-green/10 rounded-full flex items-center justify-center mx-auto mb-2 md:mb-4">
              <TrendingUp className="h-5 w-5 md:h-8 md:w-8 text-app-green" />
            </div>
            <h3 className="text-gray-600 text-xs md:text-sm font-medium mb-1">Milestones</h3>
            <span className="text-lg font-bold text-gray-900">
              {totalMilestones}
            </span>
          </CarouselItem>
        </CarouselContent>
      </Carousel>
    </div>
  );
}

