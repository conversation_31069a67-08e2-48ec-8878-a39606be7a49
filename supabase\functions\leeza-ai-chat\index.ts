
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
      }
    )

    const authHeader = req.headers.get('Authorization')!
    const { data: { user } } = await supabase.auth.getUser(authHeader.replace('Bearer ', ''))
    
    if (!user) {
      throw new Error('Unauthorized')
    }

    const { message, mode, sessionId, needsSearch = false } = await req.json()

    let searchResults = ''
    
    // If search is needed, query Tavily API
    if (needsSearch) {
      try {
        const tavilyResponse = await fetch('https://api.tavily.com/search', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer 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'
          },
          body: JSON.stringify({
            query: message,
            search_depth: "basic",
            include_answer: true,
            max_results: 3
          })
        })
        
        const searchData = await tavilyResponse.json()
        searchResults = searchData.results?.map((r: any) => `${r.title}: ${r.content}`).join('\n') || ''
      } catch (error) {
        console.error('Search error:', error)
      }
    }

    // Get patient data for context
    const { data: patientData } = await supabase
      .from('patients')
      .select(`
        *,
        therapy_goals(title, status, progress_percentage, category),
        daily_activities(title, category),
        milestones(title, status, category),
        activity_completions(completed, completion_date)
      `)
      .eq('user_id', user.id)
      .single()

    // Create mode-specific prompts
    const getSystemPrompt = (mode: string) => {
      const basePrompt = `You are Leeza AI, a compassionate and knowledgeable assistant specialized in autism therapy and support. You have access to the child's progress data and can provide personalized guidance.`
      
      switch (mode) {
        case 'caregiver':
          return `${basePrompt} You are in Caregiver Mode. Focus on providing practical advice for daily care, routines, and family support strategies. Be warm and understanding.`
        case 'therapy':
          return `${basePrompt} You are in Therapy Mode. Provide evidence-based therapeutic guidance, activity suggestions, and progress insights. Be professional yet encouraging.`
        case 'panic':
          return `${basePrompt} You are in Panic Mode. Provide immediate, calming support and crisis intervention strategies. Be soothing and offer quick, actionable steps to help manage the situation.`
        case 'progress':
          return `${basePrompt} You are in Progress Mode. Analyze the child's development data and provide detailed insights about achievements, areas for improvement, and next steps.`
        default:
          return basePrompt
      }
    }

    // Create context from patient data
    const progressContext = patientData ? `
Current Progress Data:
- Goals: ${patientData.therapy_goals?.map((g: any) => `${g.title} (${g.status}, ${g.progress_percentage}% complete)`).join(', ') || 'None'}
- Activities: ${patientData.daily_activities?.map((a: any) => `${a.title} (${a.category})`).join(', ') || 'None'}
- Milestones: ${patientData.milestones?.map((m: any) => `${m.title} (${m.status})`).join(', ') || 'None'}
- Recent Activity Completions: ${patientData.activity_completions?.filter((c: any) => c.completed).length || 0} completed recently
` : ''

    const searchContext = searchResults ? `\nSearch Results:\n${searchResults}\n` : ''

    // Make API call to Gemini using the new endpoint
    const geminiApiKey = 'AIzaSyDdPNHvgRn7KoMqU5ytFO4p3lKFeJ_iO7Y'
    const geminiResponse = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${geminiApiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: `${getSystemPrompt(mode)}

${progressContext}
${searchContext}

User message: ${message}

Please provide a helpful, personalized response based on the mode and available data.`
          }]
        }],
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.9,
          maxOutputTokens: 1024,
        },
      }),
    })

    const geminiData = await geminiResponse.json()
    const aiResponse = geminiData.candidates?.[0]?.content?.parts?.[0]?.text || "I'm sorry, I couldn't generate a response right now."

    return new Response(
      JSON.stringify({ response: aiResponse }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      },
    )
  } catch (error) {
    console.error('Error:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      },
    )
  }
})
