
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { activities } = await req.json();
    const geminiApiKey = Deno.env.get('GEMINI_API_KEY');
    
    if (!geminiApiKey) {
      throw new Error('GEMINI_API_KEY not configured');
    }

    const completedActivities = activities.filter((a: any) => a.completion?.completed);
    const completionRate = activities.length > 0 ? (completedActivities.length / activities.length) * 100 : 0;

    const activitiesContext = activities.map((activity: any) => 
      `Activity: ${activity.title} (${activity.category}) - ${activity.completion?.completed ? 'Completed' : 'Pending'}`
    ).join('\n');

    const prompt = `As an AI autism therapy assistant, analyze these daily activities and provide personalized recommendations:

${activitiesContext}

Current completion rate: ${completionRate.toFixed(1)}%

Provide encouraging recommendations (2-3 sentences) on how to improve activity completion and engagement. Be supportive and practical.`;

    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${geminiApiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: prompt
          }]
        }]
      }),
    });

    const data = await response.json();
    const recommendation = data.candidates?.[0]?.content?.parts?.[0]?.text || "Keep working on your daily activities! Consistency is key to progress.";

    return new Response(JSON.stringify({ recommendation }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Error:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
