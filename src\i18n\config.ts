
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

// Translation files
import en from './locales/en.json';
import es from './locales/es.json';
import ar from './locales/ar.json';
import te from './locales/te.json';

const resources = {
  en: { translation: en },
  es: { translation: es },
  ar: { translation: ar },
  te: { translation: te },
};

i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: 'en', // default language
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false,
    },
    react: {
      useSuspense: false,
    },
  });

export default i18n;
