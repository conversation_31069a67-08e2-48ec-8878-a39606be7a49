import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Trophy, TrendingUp, Calendar, Target, ArrowLeft } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { Layout } from "@/components/Layout";
import { Skeleton } from "@/components/ui/skeleton";
import { HorizontalDateSlider } from "@/components/ui/horizontal-date-slider";
import { SessionDetailsCard } from "@/components/ui/session-details-card";
import { format, isSameDay } from "date-fns";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";

interface Milestone {
  id: string;
  title: string;
  description: string;
  category: string;
  status: string;
  achieved_date: string;
  regression_date: string;
  notes: string;
  created_at: string;
}

export default function Milestones() {
  const [milestones, setMilestones] = useState<Milestone[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [filteredMilestones, setFilteredMilestones] = useState<Milestone[]>([]);
  const { profile } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const { t } = useTranslation();

  useEffect(() => {
    if (profile?.id) {
      fetchPatientMilestones();
    }
  }, [profile]);

  useEffect(() => {
    const filtered = milestones.filter(milestone => {
      if (!milestone.achieved_date) return true;
      return isSameDay(new Date(milestone.achieved_date), selectedDate);
    });
    setFilteredMilestones(filtered);
  }, [milestones, selectedDate]);

  const fetchPatientMilestones = async () => {
    try {
      console.log("Starting to fetch milestones for user:", profile?.id);
      
      let { data: patientData, error: patientError } = await supabase
        .from("patients")
        .select("id")
        .eq("user_id", profile?.id)
        .maybeSingle();

      console.log("Patient data:", patientData, "Error:", patientError);

      if (patientError) {
        console.error("Error fetching patient:", patientError);
        setMilestones([]);
        setLoading(false);
        return;
      }

      if (!patientData) {
        console.log("No patient record found - showing empty state");
        setMilestones([]);
        setLoading(false);
        return;
      }

      const { data: milestonesData, error: milestonesError } = await supabase
        .from("milestones")
        .select("*")
        .eq("patient_id", patientData.id)
        .order("created_at", { ascending: false });

      console.log("Milestones data:", milestonesData, "Error:", milestonesError);

      if (milestonesError) {
        console.error("Error fetching milestones:", milestonesError);
        toast({
          title: "Error",
          description: "Failed to load your milestones. Please try again.",
          variant: "destructive",
        });
        setMilestones([]);
      } else {
        setMilestones(milestonesData || []);
      }
    } catch (error: any) {
      console.error("Unexpected error:", error);
      toast({
        title: "Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
      setMilestones([]);
    } finally {
      setLoading(false);
    }
  };

  const getCategoryColor = (category: string) => {
    const colors = {
      communication: "bg-app-purple text-white",
      social: "bg-app-green text-white",
      behavior: "bg-app-yellow text-white",
      cognitive: "bg-app-pink text-white",
      motor: "bg-app-orange text-white"
    };
    return colors[category as keyof typeof colors] || "bg-gray-500 text-white";
  };

  if (loading) {
    return (
      <Layout>
        <div className="min-h-screen bg-gray-50 p-4">
          <div className="max-w-md mx-auto space-y-6">
            <Skeleton className="h-20 w-full rounded-2xl" />
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <Skeleton key={i} className="h-32 w-full rounded-2xl" />
              ))}
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white px-6 py-6 flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate(-1)}
            className="p-2"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-xl font-bold text-gray-900">{t('patient.milestones')}</h1>
        </div>

        <div className="max-w-md mx-auto px-6 space-y-6">
          {/* Date Slider */}
          <HorizontalDateSlider
            selectedDate={selectedDate}
            onDateChange={setSelectedDate}
          />

          {/* Session Details for Selected Date */}
          <SessionDetailsCard
            title={t('patient.goalsForDate')}
            performedOn={format(selectedDate, "EEEE, MMMM do, yyyy")}
            className="rounded-3xl"
          />

          {/* Stats Overview */}
          <div className="grid grid-cols-3 gap-4">
            <Card className="app-card shadow-lg rounded-3xl">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-xs font-medium text-gray-700">{t('patient.totalGoals')}</CardTitle>
                <Target className="h-4 w-4 text-app-purple" />
              </CardHeader>
              <CardContent>
                <div className="text-xl font-bold text-app-purple">{milestones.length}</div>
              </CardContent>
            </Card>

            <Card className="app-card shadow-lg rounded-3xl">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-xs font-medium text-gray-700">{t('patient.achievedCount', { count: milestones.filter(m => m.status === 'completed').length })}</CardTitle>
                <Trophy className="h-4 w-4 text-app-yellow" />
              </CardHeader>
              <CardContent>
                <div className="text-xl font-bold text-app-yellow">
                  {milestones.filter(m => m.status === 'completed').length}
                </div>
              </CardContent>
            </Card>

            <Card className="app-card shadow-lg rounded-3xl">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-xs font-medium text-gray-700">{t('patient.activeToday')}</CardTitle>
                <TrendingUp className="h-4 w-4 text-app-green" />
              </CardHeader>
              <CardContent>
                <div className="text-xl font-bold text-app-green">
                  {filteredMilestones.length}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Milestones List */}
          <div className="space-y-4">
            {(filteredMilestones.length > 0 ? filteredMilestones : milestones).length === 0 ? (
              <Card className="app-card shadow-lg rounded-3xl">
                <CardContent className="p-12 text-center">
                  <Trophy className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">{t('patient.noMilestones')}</h3>
                  <p className="text-gray-600">{t('patient.noMilestonesDescription')}</p>
                </CardContent>
              </Card>
            ) : (
              (filteredMilestones.length > 0 ? filteredMilestones : milestones).map((milestone) => (
                <Card key={milestone.id} className="app-card app-card-hover shadow-lg rounded-3xl">
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div className="space-y-3">
                        <CardTitle className="text-lg text-gray-900">{milestone.title}</CardTitle>
                        <div className="flex items-center space-x-2 flex-wrap gap-2">
                          <Badge className={getCategoryColor(milestone.category)}>
                            {t(`categories.${milestone.category}`)}
                          </Badge>
                          <Badge 
                            className={`${
                              milestone.status === 'completed' ? 'bg-green-500 text-white' :
                              milestone.status === 'in_progress' ? 'bg-yellow-500 text-white' :
                              'bg-gray-500 text-white'
                            }`}
                          >
                            {t(`status.${milestone.status}`)}
                          </Badge>
                        </div>
                      </div>
                      {milestone.status === 'completed' && (
                        <Trophy className="h-8 w-8 text-app-yellow" />
                      )}
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-gray-600">{milestone.description}</p>
                    
                    {milestone.achieved_date && (
                      <div className="flex items-center text-app-green">
                        <Calendar className="h-4 w-4 mr-2" />
                        <span className="text-sm">{t('patient.achievedOn')}: {new Date(milestone.achieved_date).toLocaleDateString()}</span>
                      </div>
                    )}

                    {milestone.notes && (
                      <div className="bg-gray-50 p-3 rounded-xl">
                        <p className="text-sm text-gray-600">{milestone.notes}</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))
            )}
          </div>

          {/* Bottom spacing */}
          <div className="h-8"></div>
        </div>
      </div>
    </Layout>
  );
}
