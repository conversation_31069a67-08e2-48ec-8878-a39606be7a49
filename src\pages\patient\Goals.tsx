
import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { Layout } from "@/components/Layout";
import { format, isSameDay, isAfter, isBefore, parseISO } from "date-fns";
import { useNavigate } from "react-router-dom";
import { useGoalsData } from "@/hooks/useGoalsData";
import { useSessionData } from "@/hooks/useSessionData";
import { GoalsDateSection } from "@/components/patient/GoalsDateSection";
import { GoalsContentSection } from "@/components/patient/GoalsContentSection";
import { TherapistAssignmentCard } from "@/components/patient/TherapistAssignmentCard";
import { PatientLoadingState } from "@/components/patient/PatientLoadingState";
import { useTranslation } from "react-i18next";

interface Goal {
  id: string;
  title: string;
  description: string;
  category: string;
  status: string;
  progress_percentage: number;
  target_date: string;
  created_at: string;
  therapy_type: string;
}

export default function Goals() {
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [filteredGoals, setFilteredGoals] = useState<Goal[]>([]);
  const navigate = useNavigate();
  const { goals, loading, aiInsight } = useGoalsData();
  const { sessionDetails, fetchSessionForDate } = useSessionData();
  const { t } = useTranslation();

  useEffect(() => {
    const filtered = goals.filter(goal => {
      if (!goal.target_date) return true;
      
      const targetDate = parseISO(goal.target_date);
      const createdDate = parseISO(goal.created_at);
      
      return (isAfter(selectedDate, createdDate) || isSameDay(selectedDate, createdDate)) &&
             (isBefore(selectedDate, targetDate) || isSameDay(selectedDate, targetDate));
    });
    setFilteredGoals(filtered);
    
    fetchSessionForDate(selectedDate);
  }, [goals, selectedDate, fetchSessionForDate]);

  if (loading) {
    return (
      <Layout>
        <PatientLoadingState />
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-md mx-auto">
          {/* Header */}
          <div className="bg-white px-6 py-6 flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate(-1)}
              className="p-2"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <h1 className="text-xl font-bold text-gray-900">{t('patient.therapyGoals')}</h1>
          </div>

          <div className="px-6 space-y-6">
            {/* Therapist Assignment Card */}
            <TherapistAssignmentCard selectedDate={selectedDate} />

            <GoalsDateSection
              selectedDate={selectedDate}
              onDateChange={setSelectedDate}
              sessionDetails={sessionDetails}
            />

            <GoalsContentSection
              filteredGoals={filteredGoals}
              selectedDate={selectedDate}
              goals={goals}
              aiInsight={aiInsight}
            />
          </div>
        </div>
      </div>
    </Layout>
  );
}
