
// Floating global voice control widget for therapist flow using useGlobalVoiceAgent hook

import React, { useRef, useState } from "react";
import { useGlobalVoiceAgent } from "@/hooks/useGlobalVoiceAgent";
import { Mi<PERSON>, MicOff, Loader2, ChevronDown, ChevronUp, Sparkles, Zap } from "lucide-react";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";

// Therapist command examples for help
const commandHelp = (
  <div className="text-xs text-app-purple space-y-1 px-2 pb-1">
    <div>• "Show all my patients"</div>
    <div>• "Open Priya's profile"</div>
    <div>• "Create goal for A<PERSON>jun"</div>
    <div>• "Schedule session tomorrow"</div>
    <div>• "Update <PERSON>'s progress to 75%"</div>
    <div>• "Generate report for Arjun"</div>
    <div>• "Go to activities"</div>
    <div>• "Sign out"</div>
  </div>
);

export const LeezaAIVoiceAgent = () => {
  const {
    state,
    startRecording,
    stopRecording,
    reset,
    isActive,
    setActive,
    transcript,
    isLoading,
    lastCommand,
    history,
    isProcessing,
    contextState
  } = useGlobalVoiceAgent();

  const [expanded, setExpanded] = useState(false);

  // Enhanced status display with context awareness
  const getStatusDisplay = () => {
    if (state === "recording") return "🎤 Recording...";
    if (isProcessing) return "🤖 Processing...";
    if (lastCommand?.confidence === "low") return "⚠️ Low confidence";
    if (lastCommand?.clarification_needed) return "❓ Needs clarification";
    if (lastCommand) return "✅ Command executed";
    return "Ready";
  };

  const getStatusColor = () => {
    if (state === "recording") return "text-red-500";
    if (isProcessing) return "text-blue-500";
    if (lastCommand?.confidence === "low") return "text-yellow-500";
    if (lastCommand?.clarification_needed) return "text-orange-500";
    if (lastCommand) return "text-green-500";
    return "text-gray-700";
  };

  // Floating widget position and z index
  return (
    <div className={`fixed bottom-8 right-8 z-[1001] select-none`}>
      {/* Expand/collapse button (bubble) */}
      {!isActive ? (
        <button
          onClick={() => { setActive(true); setExpanded(true); reset(); }}
          className="backdrop-blur-lg rounded-full p-3 shadow-lg bg-app-purple text-white hover:bg-app-purple/90 outline-none border-none focus:ring-2 focus:ring-app-purple/40 transition"
          aria-label="Activate Voice Agent"
        >
          <Mic className="w-6 h-6" />
        </button>
      ) : (
        <div className={`rounded-2xl shadow-xl bg-white/80 ring-1 ring-app-purple/30 backdrop-blur-md max-w-xs mx-auto flex flex-col`}>
          {/* Header */}
          <div className="px-4 py-2 flex items-center justify-between cursor-move">
            <span className="font-semibold flex items-center gap-2 text-app-purple">
              <Sparkles className="w-5 h-5" />
              Leeza Voice Agent
            </span>
            <button onClick={() => setActive(false)} className="ml-2 p-1 bg-app-purple/10 rounded hover:bg-app-purple/20 transition" aria-label="Close">
              <ChevronDown className="w-4 h-4" />
            </button>
          </div>
          
          {/* Animated content */}
          <div className={`transition-all ${expanded ? "max-h-[480px]" : "max-h-[200px] overflow-hidden"} px-4`}>
            {/* Enhanced Status with Context */}
            <div className="flex items-center justify-between mt-2 mb-3">
              <div className="flex gap-2 items-center">
                {state === "recording" ? (
                  <Mic className="text-red-500 animate-pulse w-5 h-5" />
                ) : isProcessing ? (
                  <Loader2 className="text-app-purple animate-spin w-5 h-5" />
                ) : (
                  <Mic className="text-gray-400 w-5 h-5" />
                )}
                <span className={`text-sm ${getStatusColor()}`}>
                  {getStatusDisplay()}
                </span>
              </div>
              <button
                onClick={() => setExpanded((v) => !v)}
                className="text-app-purple/60 hover:text-app-purple p-1 rounded"
                aria-label={expanded ? "Collapse" : "Expand"}
              >
                {expanded ? <ChevronDown className="w-4 h-4" /> : <ChevronUp className="w-4 h-4" />}
              </button>
            </div>

            {/* Context Indicator */}
            {expanded && contextState?.lastPatient && (
              <div className="bg-blue-50 border border-blue-200 rounded p-2 mb-3 text-xs">
                <span className="text-blue-700">Context: Last patient - {contextState.lastPatient}</span>
              </div>
            )}

            {/* Controls */}
            <div className="mb-3">
              {state !== "recording" ? (
                <Button 
                  onClick={startRecording} 
                  className="bg-app-purple w-full" 
                  disabled={isLoading || isProcessing}
                >
                  <Mic className="mr-2 w-4 h-4" /> 
                  {isProcessing ? "Processing..." : "Start Recording"}
                </Button>
              ) : (
                <Button onClick={stopRecording} className="bg-red-500 hover:bg-red-600 w-full">
                  <MicOff className="mr-2 w-4 h-4" /> Stop Recording
                </Button>
              )}
            </div>

            {/* Auto-processing indicator with enhanced feedback */}
            {(isProcessing || transcript) && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-3">
                <div className="flex items-center gap-2 mb-2">
                  <Zap className="w-4 h-4 text-blue-600" />
                  <span className="text-sm font-medium text-blue-800">Auto-Processing</span>
                </div>
                
                {/* Transcription */}
                {transcript && (
                  <div className="bg-white border rounded p-2 mb-2">
                    <span className="block text-xs text-gray-600 mb-1">Transcription:</span>
                    <span className="block text-sm font-mono text-gray-900">{transcript}</span>
                  </div>
                )}

                {/* Processing steps with enhanced status */}
                <div className="text-xs space-y-1">
                  <div className={`flex items-center gap-2 ${transcript ? "text-green-600" : "text-gray-500"}`}>
                    {transcript ? "✅" : "⏳"} Speech-to-text
                  </div>
                  <div className={`flex items-center gap-2 ${lastCommand ? "text-green-600" : transcript ? "text-blue-600" : "text-gray-500"}`}>
                    {lastCommand ? "✅" : transcript ? "⏳" : "⏸️"} Command parsing
                    {lastCommand?.confidence && (
                      <span className={`text-xs px-1 rounded ${
                        lastCommand.confidence === 'high' ? 'bg-green-100 text-green-800' :
                        lastCommand.confidence === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {lastCommand.confidence}
                      </span>
                    )}
                  </div>
                  <div className={`flex items-center gap-2 ${lastCommand ? "text-green-600" : "text-gray-500"}`}>
                    {lastCommand ? "✅" : "⏸️"} Auto-execution
                  </div>
                </div>
              </div>
            )}

            {/* Enhanced Command output */}
            {lastCommand?.response && (
              <div className="bg-app-purple/10 p-2 rounded text-app-purple text-xs mb-3">
                <div className="font-semibold mb-1">Command: {lastCommand.action}</div>
                <div>{lastCommand.response}</div>
                {lastCommand.clarification_needed && (
                  <div className="mt-1 text-orange-600 font-medium">
                    ❓ {lastCommand.clarification_needed}
                  </div>
                )}
              </div>
            )}

            {/* Reset button */}
            {(transcript || lastCommand) && (
              <Button
                className="w-full mb-3"
                variant="outline"
                onClick={reset}
                disabled={isLoading || isProcessing}
              >
                Reset & Clear
              </Button>
            )}

            {/* Command history with context */}
            {history.length > 0 && expanded && (
              <div className="mb-4">
                <div className="text-app-purple font-semibold text-xs mb-2">Recent Commands</div>
                <div className="flex flex-col gap-1 max-h-24 overflow-y-auto">
                  {[...history].slice(-3).reverse().map((cmd, i) => (
                    <div key={i} className="bg-white rounded px-2 py-1 border text-xs text-gray-700">
                      <div className="flex items-center gap-1">
                        <b>{cmd.action}</b>
                        {cmd.confidence && (
                          <span className={`text-xs px-1 rounded ${
                            cmd.confidence === 'high' ? 'bg-green-100 text-green-600' :
                            cmd.confidence === 'medium' ? 'bg-yellow-100 text-yellow-600' :
                            'bg-red-100 text-red-600'
                          }`}>
                            {cmd.confidence}
                          </span>
                        )}
                      </div>
                      <div className="text-gray-600">{cmd.response || "-"}</div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Help */}
            {expanded && (
              <div>
                <span className="block text-app-purple font-bold mb-1 text-xs">Try saying:</span>
                {commandHelp}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
