{"common": {"loading": "Cargando...", "error": "Error", "save": "Guardar", "cancel": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON>", "delete": "Eliminar", "add": "Agregar", "search": "Buscar", "filter": "Filtrar", "back": "Atrás", "next": "Siguient<PERSON>", "previous": "Anterior", "submit": "Enviar", "close": "<PERSON><PERSON><PERSON>", "yes": "Sí", "no": "No", "confirm": "Confirmar", "dashboard": "Panel de Control", "profile": "Perfil", "settings": "Configuración", "logout": "<PERSON><PERSON><PERSON>", "signin": "<PERSON><PERSON><PERSON>", "signup": "Registrarse", "welcome": "Bienvenido", "home": "<PERSON><PERSON>o", "success": "Éxito"}, "auth": {"title": "Leeza AI", "subtitle": "Sistema de Seguimiento de Progreso", "patientPortal": "Portal del Paciente", "patientDescription": "Rastrea tu progreso y actividades de terapia", "therapistPortal": "Portal del Terapeuta", "therapistDescription": "Gestiona pacientes y programas de terapia", "adminPortal": "Portal del Administrador", "adminDescription": "Administración y gestión del sistema", "email": "Correo Electrónico", "password": "Contraseña", "firstName": "Nombre", "lastName": "Apellido", "guardianName": "Nombre del Tutor", "dateOfBirth": "<PERSON><PERSON> de Nacimiento", "autismLevel": "<PERSON><PERSON>", "emergencyContact": "Contacto de Emergencia", "signingIn": "Iniciando se<PERSON>...", "creatingAccount": "<PERSON><PERSON>ndo cuenta...", "signUpAs": "Registrarse como {{role}}", "checkEmail": "Revisa tu correo para el enlace de verificación.", "welcomeBack": "¡Bienvenido de vuelta!", "signedInSuccessfully": "Has iniciado sesión exitosamente."}, "dashboard": {"welcomeBack": "¡Bienvenido de vuelta, {{name}}!", "overview": "Resumen", "quickActions": "Acciones Rápidas", "recentActivity": "Actividad Reciente", "statistics": "Estadísticas"}, "patient": {"goals": "Objetivos", "activities": "Actividades", "sessions": "Sesiones", "milestones": "<PERSON><PERSON>", "game": "<PERSON><PERSON>bilidades", "therapyGoals": "Objetivos de Terapia", "myActivities": "Mis Actividades", "mySessions": "Mis Sesiones", "myMilestones": "<PERSON><PERSON>", "progress": "Progreso", "completed": "Completado", "active": "Activo", "scheduled": "Programado", "noGoalsDate": "Sin Objetivos para Esta Fecha", "noGoalsDateDescription": "No hay objetivos de terapia programados para {{date}}.", "aiInsights": "Perspectivas de IA", "totalGoals": "Objetivos Totales", "activeToday": "Activos Hoy", "goalsForDate": "Objetivos para la Fecha Seleccionada", "sessionDetails": "Detalles de la Sesión", "duration": "Duración", "therapyType": "Tipo de Terapia", "therapist": "Terapeuta", "virtual": "Virtual", "inPerson": "Presencial", "target": "Objetivo", "category": "Categoría", "defaultName": "Paciente", "yourTherapist": "Tu terapeuta: {{name}}", "waitingTherapistAssignment": "¡Tu cuenta ha sido creada! Esperando asignación de terapeuta.", "pendingTherapistAssignment": "Asignación de Terapeuta Pendiente", "therapistAssigned": "Terapeuta <PERSON>ignado", "leezaAIAssistant": "<PERSON><PERSON><PERSON>", "aiCompanionDescription": "Tu compañero personal de IA para apoyo y orientación", "available247": "Disponible 24/7", "aiPowered": "Impulsado por IA", "autismLevel": "<PERSON><PERSON>", "activeGoalsCount": "{{count}} objetivos activos", "completedTodayCount": "{{completed}}/{{total}} completados hoy", "achievedCount": "{{count}} logrados", "upcomingCount": "{{count}} pr<PERSON><PERSON><PERSON>", "gameDescription": "Juega juegos para mejorar habilidades motoras, cognitivas y del habla", "new": "¡Nuevo!", "noMilestones": "Sin Hitos Aún", "noMilestonesDescription": "Tu terapeuta rastreará hitos importantes en tu progreso.", "achievedOn": "Logrado el", "sessionPreparation": "Preparación de Sesión con IA", "noSessions": "Sin Sesiones Programadas", "noSessionsDescription": "Tu terapeuta programará sesiones para ti. Vuelve más tarde.", "session": "Sesión", "focusAreas": "<PERSON><PERSON><PERSON>", "minutes": "minutos", "joinSession": "Unirse a la Sesión", "chooseGameMode": "<PERSON><PERSON><PERSON>", "score": "Puntuación", "level": "<PERSON><PERSON>", "start": "Iniciar", "sayTheWord": "Di la palabra", "startSpeaking": "Em<PERSON>zar a <PERSON>blar", "tapOnlyColor": "<PERSON><PERSON> solo", "shapes": "formas", "howToPlay": "<PERSON><PERSON><PERSON>", "motorInstructions": "Toca las formas lo más rápido posible para mejorar la coordinación ojo-mano", "cognitiveInstructions": "Solo toca formas del color especificado para desafiar tu concentración y atención", "speechInstructions": "Di la palabra claramente en tu micrófono para practicar el habla y la pronunciación"}, "therapist": {"patients": "<PERSON><PERSON>", "patientManagement": "Gestión de Pacientes", "goalsManagement": "Gestión de Objetivos", "activitiesManagement": "Gestión de Actividades", "sessionsManagement": "Gestión de Sesiones", "totalPatients": "Total de Pacientes", "activeSessions": "Sesiones Activas", "completedGoals": "Objetivos Completados", "upcomingSessions": "Próximas Sesiones", "manageAndTrackPatients": "Gestiona y rastrea tus pacientes asignados", "assignedToYou": "Asignados a ti", "activeGoals": "Objetivos Activos", "totalGoals": "Objetivos totales", "avgCompletion": "Completado Promedio", "activityCompletion": "Completado de actividades", "patientOverview": "Resumen de Pacientes", "clickToManage": "Haz clic en cualquier paciente para gestionar su plan de terapia", "noPatientsAssigned": "Sin Pacientes Asignados", "contactAdministrator": "Contacta a tu administrador para que te asignen pacientes.", "yearsOld": "{{age}} años", "complete": "Completo", "selectPatient": "Se<PERSON><PERSON><PERSON><PERSON> paciente", "unknownPatient": "Paciente Desconocido", "searchActivities": "Buscar actividades...", "createActivity": "Crear Actividad", "createNewActivity": "Crear Nueva Actividad", "activityTitle": "Título de la actividad", "activityDescription": "Descripción de la actividad", "selectCategory": "Seleccionar categoría", "selfCare": "Cuidado Personal", "academic": "Académico", "scheduledTime": "Hora programada", "startDate": "Fecha de inicio", "endDate": "<PERSON><PERSON> de fin", "noActivitiesCreated": "Sin Actividades Creadas", "startByCreatingActivities": "Comienza creando actividades diarias para tus pacientes.", "inactive": "Inactivo", "anytime": "<PERSON><PERSON><PERSON><PERSON> momento", "patient": "Paciente"}, "admin": {"patientManagement": "Gestión de Pacientes", "therapistManagement": "Gestión de Terapeutas", "systemReports": "Reportes del Sistema", "totalUsers": "Total de Usuarios", "activeTherapists": "Terapeutas Activos", "totalPatients": "Total de Pacientes", "systemHealth": "Salud del Sistema", "failedToFetchTherapists": "Error al obtener terapeutas", "therapistProfile": "Perfil del Terapeuta", "managingPatients": "Gestionando {{count}} pacientes", "searchTherapists": "Buscar terapeutas...", "addTherapist": "Agregar <PERSON>", "noTherapistsFound": "No se Encontraron Terapeutas", "noTherapistsMatch": "Ningún terapeuta coincide con tus criterios de búsqueda.", "noTherapistsRegistered": "Aún no se han registrado terapeutas.", "patientCount": "{{count}} <PERSON><PERSON><PERSON>", "joined": "Se unió", "noPatientsAssigned": "Sin pacientes asignados", "viewProfile": "<PERSON><PERSON>", "manage": "Gestionar", "failedToFetchData": "Error al obtener datos", "patientProfile": "Perfil del Paciente", "searchPatients": "Buscar pacientes...", "addPatient": "<PERSON><PERSON><PERSON><PERSON>", "noPatientsFound": "No se Encontraron Pacientes", "noPatientsMatch": "Ningún paciente coincide con tus criterios de búsqueda.", "noPatientsRegistered": "Aún no se han registrado pacientes.", "guardian": "Tutor", "notSpecified": "No especificado", "registered": "Registrado", "assignedToTherapist": "Asignado a terapeuta", "awaitingTherapistAssignment": "Esperando asignación de terapeuta", "comprehensiveAnalytics": "Análisis e insights integrales", "aiSystemAnalysis": "Análisis del Sistema IA", "totalSessions": "Total de Sesiones", "goalCompletion": "Completado de Objetivos", "fromLastMonth": "+{{count}} del mes pasado", "thisWeekIncrease": "+12% esta semana", "percentFromLastMonth": "+{{percent}}% del mes pasado", "therapyGoalsProgress": "Progreso de Objetivos de Terapia", "goalCompletionOverview": "Resumen del estado de completado de objetivos", "completedGoals": "Objetivos Completados", "activityCompletion": "Completado de Actividades", "milestonesAchieved": "Hitos Logrados", "milestoneStatus": "Estado de Hitos", "milestoneProgressDistribution": "Distribución actual del progreso de hitos", "regressed": "Retrocedido", "sessionTrends": "Tendencias de Sesiones", "monthlySessionDistribution": "Distribución mensual de sesiones", "goalStatusDistribution": "Distribución del Estado de Objetivos", "currentGoalStatusBreakdown": "Desglose actual del estado de objetivos", "managePatient": "Gestionar <PERSON>e", "patientName": "Nombre del Paciente", "assignedTherapist": "Terapeuta <PERSON>ignado", "selectTherapist": "Seleccionar un terapeuta", "noTherapist": "Sin terapeuta asignado", "medicalNotes": "Notas Médicas", "medicalNotesPlaceholder": "Ingrese cualquier nota médica u observación relevante...", "emergencyContactPlaceholder": "Ingrese información de contacto de emergencia...", "updating": "Actualizando...", "updatePatient": "<PERSON><PERSON><PERSON><PERSON>", "patientUpdatedSuccessfully": "Paciente actualizado exitosamente", "failedToUpdatePatient": "<PERSON><PERSON>r al actualizar paciente", "manageTherapist": "Gestionar Terapeuta", "updateTherapist": "<PERSON><PERSON><PERSON><PERSON>", "therapistUpdatedSuccessfully": "Terapeuta actualizado exitosamente", "failedToUpdateTherapist": "Error al actualizar terapeuta"}, "therapyTypes": {"aba_therapy": "Terapia ABA", "speech_therapy": "Terapia del Habla", "occupational_therapy": "Terapia Ocupacional", "behavioral_therapy": "Terapia Conductual"}, "categories": {"communication": "Comunicación", "social": "Social", "behavior": "Comportamiento", "cognitive": "Cognitivo", "motor": "Motor"}, "status": {"active": "Activo", "completed": "Completado", "paused": "<PERSON><PERSON><PERSON>", "pending": "Pendiente", "cancelled": "Cancelado", "in_progress": "En Progreso"}, "autismLevels": {"1": "Nivel 1", "2": "Nivel 2", "3": "Nivel 3"}, "days": {"monday": "<PERSON><PERSON>", "tuesday": "<PERSON><PERSON>", "wednesday": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thursday": "<PERSON><PERSON>", "friday": "Viernes", "saturday": "Sábado", "sunday": "Domingo"}, "months": {"january": "<PERSON><PERSON>", "february": "<PERSON><PERSON><PERSON>", "march": "<PERSON><PERSON>", "april": "Abril", "may": "Mayo", "june": "<PERSON><PERSON>", "july": "<PERSON>", "august": "Agosto", "september": "Septiembre", "october": "Octubre", "november": "Noviembre", "december": "Diciembre"}}