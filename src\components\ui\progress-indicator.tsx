
import { Progress } from "@/components/ui/progress";
import { CheckCircle, Circle } from "lucide-react";

interface ProgressIndicatorProps {
  value: number;
  max?: number;
  label?: string;
  variant?: 'default' | 'success' | 'warning' | 'info';
  size?: 'sm' | 'md' | 'lg';
  showPercentage?: boolean;
  showSteps?: boolean;
  steps?: { label: string; completed: boolean }[];
}

const variantColors = {
  default: 'text-app-purple',
  success: 'text-app-green',
  warning: 'text-app-yellow',
  info: 'text-blue-600',
};

const progressColors = {
  default: '[&>[data-state=complete]]:bg-app-purple',
  success: '[&>[data-state=complete]]:bg-app-green',
  warning: '[&>[data-state=complete]]:bg-app-yellow',
  info: '[&>[data-state=complete]]:bg-blue-600',
};

export function ProgressIndicator({
  value,
  max = 100,
  label,
  variant = 'default',
  size = 'md',
  showPercentage = true,
  showSteps = false,
  steps = [],
}: ProgressIndicatorProps) {
  const percentage = Math.round((value / max) * 100);
  
  const sizeClasses = {
    sm: 'h-2',
    md: 'h-3',
    lg: 'h-4',
  };

  if (showSteps && steps.length > 0) {
    return (
      <div className="space-y-4">
        {label && (
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">{label}</span>
            {showPercentage && (
              <span className={`text-sm font-semibold ${variantColors[variant]}`}>
                {percentage}%
              </span>
            )}
          </div>
        )}
        
        <div className="space-y-2">
          {steps.map((step, index) => (
            <div key={index} className="flex items-center space-x-3">
              {step.completed ? (
                <CheckCircle className={`h-5 w-5 ${variantColors[variant]}`} />
              ) : (
                <Circle className="h-5 w-5 text-gray-300" />
              )}
              <span
                className={`text-sm ${
                  step.completed ? 'text-gray-900 font-medium' : 'text-gray-500'
                }`}
              >
                {step.label}
              </span>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      {label && (
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-700">{label}</span>
          {showPercentage && (
            <span className={`text-sm font-semibold ${variantColors[variant]}`}>
              {percentage}%
            </span>
          )}
        </div>
      )}
      
      <Progress
        value={percentage}
        className={`${sizeClasses[size]} ${progressColors[variant]}`}
      />
    </div>
  );
}
