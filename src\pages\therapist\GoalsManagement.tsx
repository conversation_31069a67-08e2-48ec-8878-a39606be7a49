import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Target, Plus, Search, ArrowLeft, Edit, TrendingUp } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { Layout } from "@/components/Layout";
import { Skeleton } from "@/components/ui/skeleton";
import { useNavigate, use<PERSON><PERSON>chP<PERSON><PERSON> } from "react-router-dom";

type GoalStatus = "active" | "completed" | "paused" | "archived";

interface Goal {
  id: string;
  title: string;
  description: string;
  category: string;
  status: GoalStatus;
  progress_percentage: number;
  target_date: string;
  therapy_type: string;
  patient: {
    guardian_name: string;
    user: {
      first_name: string;
      last_name: string;
    } | null;
  } | null;
}

interface Patient {
  id: string;
  guardian_name: string;
  user: {
    first_name: string;
    last_name: string;
  } | null;
}

export default function GoalsManagement() {
  const [goals, setGoals] = useState<Goal[]>([]);
  const [patients, setPatients] = useState<Patient[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingGoal, setEditingGoal] = useState<Goal | null>(null);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showProgressDialog, setShowProgressDialog] = useState(false);
  const [selectedGoal, setSelectedGoal] = useState<Goal | null>(null);
  const [progressUpdate, setProgressUpdate] = useState({
    progress_percentage: 0,
    status: "active" as GoalStatus
  });
  const [newGoal, setNewGoal] = useState({
    patient_id: "",
    title: "",
    description: "",
    category: "behavior",
    target_date: "",
    therapy_type: "aba_therapy"
  });
  const { profile } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  useEffect(() => {
    if (profile?.id) {
      fetchGoals();
      fetchPatients();
      
      // Set patient from URL parameter
      const patientId = searchParams.get('patient');
      if (patientId) {
        setNewGoal(prev => ({ ...prev, patient_id: patientId }));
      }
    }
  }, [profile, searchParams]);

  const fetchGoals = async () => {
    if (!profile?.id) {
      console.log("No profile ID available for goals");
      setLoading(false);
      return;
    }

    try {
      console.log("Fetching goals for therapist:", profile.id);
      const { data, error } = await supabase
        .from("therapy_goals")
        .select(`
          *,
          patient:patients(
            guardian_name,
            user:profiles!patients_user_id_fkey(first_name, last_name)
          )
        `)
        .eq("therapist_id", profile.id)
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching goals:", error);
        throw error;
      }
      
      console.log("Goals fetched successfully:", data);
      setGoals((data as any) || []);
    } catch (error: any) {
      console.error("Goals fetch error:", error);
      toast({
        title: "Error",
        description: "Failed to fetch goals",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchPatients = async () => {
    if (!profile?.id) {
      console.log("No profile ID available for patients");
      return;
    }

    try {
      console.log("Fetching patients for therapist:", profile.id);
      const { data, error } = await supabase
        .from("patients")
        .select(`
          id,
          guardian_name,
          user:profiles!patients_user_id_fkey(first_name, last_name)
        `)
        .eq("assigned_therapist_id", profile.id);

      if (error) {
        console.error("Error fetching patients:", error);
        throw error;
      }
      
      console.log("Patients fetched successfully:", data);
      setPatients((data as any) || []);
    } catch (error: any) {
      console.error("Patients fetch error:", error);
      toast({
        title: "Error",
        description: "Failed to fetch patients",
        variant: "destructive",
      });
    }
  };

  const createGoal = async () => {
    if (!profile?.id) {
      toast({
        title: "Error",
        description: "User profile not loaded",
        variant: "destructive",
      });
      return;
    }

    if (!newGoal.patient_id || !newGoal.title) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    try {
      const { error } = await supabase
        .from("therapy_goals")
        .insert({
          patient_id: newGoal.patient_id,
          therapist_id: profile.id,
          title: newGoal.title,
          description: newGoal.description,
          category: newGoal.category,
          target_date: newGoal.target_date || null,
          therapy_type: newGoal.therapy_type,
          status: 'active',
          progress_percentage: 0
        });

      if (error) throw error;

      toast({
        title: "Success",
        description: "Goal created successfully",
      });

      setShowCreateForm(false);
      setNewGoal({
        patient_id: "",
        title: "",
        description: "",
        category: "behavior",
        target_date: "",
        therapy_type: "aba_therapy"
      });
      fetchGoals();
    } catch (error: any) {
      toast({
        title: "Error",
        description: "Failed to create goal",
        variant: "destructive",
      });
    }
  };

  const handleEditGoal = (goal: Goal) => {
    setEditingGoal({...goal});
    setShowEditDialog(true);
  };

  const updateGoal = async () => {
    if (!editingGoal) return;

    try {
      const { error } = await supabase
        .from("therapy_goals")
        .update({
          title: editingGoal.title,
          description: editingGoal.description,
          category: editingGoal.category,
          target_date: editingGoal.target_date || null,
          therapy_type: editingGoal.therapy_type,
          status: editingGoal.status
        })
        .eq("id", editingGoal.id);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Goal updated successfully",
      });

      setShowEditDialog(false);
      setEditingGoal(null);
      fetchGoals();
    } catch (error: any) {
      toast({
        title: "Error",
        description: "Failed to update goal",
        variant: "destructive",
      });
    }
  };

  const handleUpdateProgress = (goal: Goal) => {
    setSelectedGoal(goal);
    setProgressUpdate({
      progress_percentage: goal.progress_percentage,
      status: goal.status
    });
    setShowProgressDialog(true);
  };

  const updateProgress = async () => {
    if (!selectedGoal) return;

    try {
      const { error } = await supabase
        .from("therapy_goals")
        .update({
          progress_percentage: progressUpdate.progress_percentage,
          status: progressUpdate.status
        })
        .eq("id", selectedGoal.id);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Progress updated successfully",
      });

      setShowProgressDialog(false);
      setSelectedGoal(null);
      fetchGoals();
    } catch (error: any) {
      toast({
        title: "Error",
        description: "Failed to update progress",
        variant: "destructive",
      });
    }
  };

  const getPatientDisplayName = (patient: Patient) => {
    if (patient.user?.first_name && patient.user?.last_name) {
      return `${patient.user.first_name} ${patient.user.last_name}`;
    }
    return patient.guardian_name || 'Unknown Patient';
  };

  const getSelectedPatientName = () => {
    const selectedPatient = patients.find(p => p.id === newGoal.patient_id);
    return selectedPatient ? getPatientDisplayName(selectedPatient) : 'Select patient';
  };

  const filteredGoals = goals.filter(goal =>
    goal.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    goal.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    goal.patient?.user?.first_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    goal.patient?.user?.last_name?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <Layout>
        <div className="min-h-screen bg-gray-50 p-6">
          <div className="max-w-4xl mx-auto space-y-6">
            <Skeleton className="h-12 w-64" />
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <Skeleton key={i} className="h-32 rounded-2xl" />
              ))}
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white px-6 py-6 flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate("/dashboard")}
            className="p-2"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-2xl font-bold text-gray-900">Goals Management</h1>
        </div>

        <div className="max-w-4xl mx-auto px-6 space-y-6">
          {/* Search and Actions */}
          <div className="flex items-center justify-between gap-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search goals..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 h-12 rounded-xl border-gray-200"
              />
            </div>
            <Button 
              onClick={() => setShowCreateForm(!showCreateForm)}
              className="bg-app-yellow hover:bg-app-yellow/90 text-white rounded-xl h-12 px-6"
            >
              <Plus className="h-4 w-4 mr-2" />
              Create Goal
            </Button>
          </div>

          {/* Create Goal Form */}
          {showCreateForm && (
            <Card className="bg-white border-0 shadow-lg rounded-2xl">
              <CardHeader>
                <CardTitle className="text-app-yellow">Create New Goal</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Select Patient *
                  </label>
                  <Select value={newGoal.patient_id} onValueChange={(value) => setNewGoal({...newGoal, patient_id: value})}>
                    <SelectTrigger className="rounded-xl">
                      <SelectValue placeholder={getSelectedPatientName()} />
                    </SelectTrigger>
                    <SelectContent>
                      {patients.map((patient) => (
                        <SelectItem key={patient.id} value={patient.id}>
                          {getPatientDisplayName(patient)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Goal Title *
                  </label>
                  <Input 
                    placeholder="Enter goal title" 
                    value={newGoal.title}
                    onChange={(e) => setNewGoal({...newGoal, title: e.target.value})}
                    className="rounded-xl" 
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Goal Description
                  </label>
                  <Textarea 
                    placeholder="Describe the goal in detail" 
                    value={newGoal.description}
                    onChange={(e) => setNewGoal({...newGoal, description: e.target.value})}
                    className="rounded-xl" 
                    rows={4}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Category
                  </label>
                  <Select value={newGoal.category} onValueChange={(value) => setNewGoal({...newGoal, category: value})}>
                    <SelectTrigger className="rounded-xl">
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="behavior">Behavior</SelectItem>
                      <SelectItem value="communication">Communication</SelectItem>
                      <SelectItem value="social">Social</SelectItem>
                      <SelectItem value="academic">Academic</SelectItem>
                      <SelectItem value="self-care">Self Care</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Target Date
                  </label>
                  <Input 
                    type="date" 
                    value={newGoal.target_date}
                    onChange={(e) => setNewGoal({...newGoal, target_date: e.target.value})}
                    className="rounded-xl" 
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Therapy Type
                  </label>
                  <Select value={newGoal.therapy_type} onValueChange={(value) => setNewGoal({...newGoal, therapy_type: value})}>
                    <SelectTrigger className="rounded-xl">
                      <SelectValue placeholder="Select therapy type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="aba_therapy">ABA Therapy</SelectItem>
                      <SelectItem value="speech_therapy">Speech Therapy</SelectItem>
                      <SelectItem value="occupational_therapy">Occupational Therapy</SelectItem>
                      <SelectItem value="physical_therapy">Physical Therapy</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex space-x-2 pt-4">
                  <Button 
                    variant="outline" 
                    onClick={() => setShowCreateForm(false)}
                    className="rounded-xl"
                  >
                    Cancel
                  </Button>
                  <Button 
                    onClick={createGoal}
                    className="bg-app-yellow hover:bg-app-yellow/90 text-white rounded-xl"
                    disabled={!newGoal.patient_id || !newGoal.title}
                  >
                    Create Goal
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Edit Goal Dialog */}
          <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Edit Goal</DialogTitle>
              </DialogHeader>
              {editingGoal && (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Goal Title *
                    </label>
                    <Input 
                      placeholder="Enter goal title" 
                      value={editingGoal.title}
                      onChange={(e) => setEditingGoal({...editingGoal, title: e.target.value})}
                      className="rounded-xl" 
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Goal Description
                    </label>
                    <Textarea 
                      placeholder="Describe the goal in detail" 
                      value={editingGoal.description}
                      onChange={(e) => setEditingGoal({...editingGoal, description: e.target.value})}
                      className="rounded-xl" 
                      rows={4}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Category
                    </label>
                    <Select value={editingGoal.category} onValueChange={(value) => setEditingGoal({...editingGoal, category: value})}>
                      <SelectTrigger className="rounded-xl">
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="behavior">Behavior</SelectItem>
                        <SelectItem value="communication">Communication</SelectItem>
                        <SelectItem value="social">Social</SelectItem>
                        <SelectItem value="academic">Academic</SelectItem>
                        <SelectItem value="self-care">Self Care</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Target Date
                    </label>
                    <Input 
                      type="date" 
                      value={editingGoal.target_date}
                      onChange={(e) => setEditingGoal({...editingGoal, target_date: e.target.value})}
                      className="rounded-xl" 
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Therapy Type
                    </label>
                    <Select value={editingGoal.therapy_type} onValueChange={(value) => setEditingGoal({...editingGoal, therapy_type: value})}>
                      <SelectTrigger className="rounded-xl">
                        <SelectValue placeholder="Select therapy type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="aba_therapy">ABA Therapy</SelectItem>
                        <SelectItem value="speech_therapy">Speech Therapy</SelectItem>
                        <SelectItem value="occupational_therapy">Occupational Therapy</SelectItem>
                        <SelectItem value="physical_therapy">Physical Therapy</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Status
                    </label>
                    <Select value={editingGoal.status} onValueChange={(value: GoalStatus) => setEditingGoal({...editingGoal, status: value})}>
                      <SelectTrigger className="rounded-xl">
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="completed">Completed</SelectItem>
                        <SelectItem value="paused">Paused</SelectItem>
                        <SelectItem value="archived">Archived</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex space-x-2 pt-4">
                    <Button 
                      variant="outline" 
                      onClick={() => setShowEditDialog(false)}
                      className="rounded-xl"
                    >
                      Cancel
                    </Button>
                    <Button 
                      onClick={updateGoal}
                      className="bg-app-yellow hover:bg-app-yellow/90 text-white rounded-xl"
                    >
                      Update Goal
                    </Button>
                  </div>
                </div>
              )}
            </DialogContent>
          </Dialog>

          {/* Progress Update Dialog */}
          <Dialog open={showProgressDialog} onOpenChange={setShowProgressDialog}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Update Progress</DialogTitle>
              </DialogHeader>
              {selectedGoal && (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Goal: {selectedGoal.title}
                    </label>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Progress Percentage (0-100)
                    </label>
                    <Input 
                      type="number"
                      min="0"
                      max="100"
                      value={progressUpdate.progress_percentage}
                      onChange={(e) => setProgressUpdate({...progressUpdate, progress_percentage: parseInt(e.target.value) || 0})}
                      className="rounded-xl" 
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Status
                    </label>
                    <Select value={progressUpdate.status} onValueChange={(value: GoalStatus) => setProgressUpdate({...progressUpdate, status: value})}>
                      <SelectTrigger className="rounded-xl">
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="completed">Completed</SelectItem>
                        <SelectItem value="paused">Paused</SelectItem>
                        <SelectItem value="archived">Archived</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex space-x-2 pt-4">
                    <Button 
                      variant="outline" 
                      onClick={() => setShowProgressDialog(false)}
                      className="rounded-xl"
                    >
                      Cancel
                    </Button>
                    <Button 
                      onClick={updateProgress}
                      className="bg-app-yellow hover:bg-app-yellow/90 text-white rounded-xl"
                    >
                      Update Progress
                    </Button>
                  </div>
                </div>
              )}
            </DialogContent>
          </Dialog>

          {/* Goals List */}
          <div className="space-y-4">
            {goals.length === 0 ? (
              <Card className="bg-white border-0 shadow-lg rounded-2xl">
                <CardContent className="p-12 text-center">
                  <Target className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">No Goals Created</h3>
                  <p className="text-gray-600">Start by creating therapy goals for your patients.</p>
                </CardContent>
              </Card>
            ) : (
              filteredGoals.map((goal) => (
                <Card key={goal.id} className="bg-white border-0 shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300">
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div className="space-y-3">
                        <CardTitle className="text-lg text-gray-900">{goal.title}</CardTitle>
                        <div className="flex items-center space-x-3">
                          <Badge className="bg-app-yellow text-white">
                            {goal.category}
                          </Badge>
                          <Badge 
                            className={`${
                              goal.status === 'completed' ? 'bg-green-500 text-white' :
                              goal.status === 'active' ? 'bg-blue-500 text-white' :
                              'bg-gray-500 text-white'
                            }`}
                          >
                            {goal.status}
                          </Badge>
                        </div>
                      </div>
                      <div className="text-right">
                        <span className="text-sm text-gray-600">Progress</span>
                        <div className="text-2xl font-bold text-app-yellow">
                          {goal.progress_percentage}%
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-gray-600">{goal.description}</p>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-600 mb-1">Patient:</p>
                        <p className="font-semibold text-gray-900">
                          {goal.patient?.user?.first_name || 'Unknown'} {goal.patient?.user?.last_name || 'Patient'}
                        </p>
                      </div>
                      <div className="flex space-x-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleEditGoal(goal)}
                          className="rounded-xl border-app-yellow text-app-yellow hover:bg-app-yellow hover:text-white"
                        >
                          <Edit className="h-4 w-4 mr-1" />
                          Edit
                        </Button>
                        <Button 
                          size="sm"
                          onClick={() => handleUpdateProgress(goal)}
                          className="bg-app-yellow hover:bg-app-yellow/90 text-white rounded-xl"
                        >
                          <TrendingUp className="h-4 w-4 mr-1" />
                          Update Progress
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>

          {/* Bottom spacing */}
          <div className="h-8"></div>
        </div>
      </div>
    </Layout>
  );
}
