
import { Skeleton } from "@/components/ui/skeleton";

interface EnhancedLoadingProps {
  type?: 'dashboard' | 'card' | 'list' | 'full';
  className?: string;
}

export function EnhancedLoading({ type = 'dashboard', className }: EnhancedLoadingProps) {
  if (type === 'full') {
    return (
      <div className={`min-h-screen bg-gray-50 flex items-center justify-center ${className}`}>
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-app-purple/20 border-t-app-purple rounded-full animate-spin mx-auto mb-4"></div>
          <Skeleton className="h-4 w-32 mx-auto mb-2" />
          <Skeleton className="h-3 w-24 mx-auto" />
        </div>
      </div>
    );
  }

  if (type === 'dashboard') {
    return (
      <div className={`min-h-screen bg-gray-50 p-6 ${className}`}>
        <div className="max-w-4xl mx-auto space-y-8">
          {/* Header skeleton */}
          <div className="bg-white shadow-sm border-b rounded-xl p-8">
            <div className="text-center">
              <Skeleton className="w-20 h-20 rounded-full mx-auto mb-4" />
              <Skeleton className="h-8 w-64 mx-auto mb-2" />
              <Skeleton className="h-5 w-48 mx-auto mb-4" />
              <Skeleton className="h-6 w-32 mx-auto" />
            </div>
          </div>
          
          {/* Cards skeleton */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <Skeleton key={i} className="h-40 w-full rounded-xl" />
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (type === 'card') {
    return (
      <div className={`space-y-4 ${className}`}>
        <Skeleton className="h-8 w-full" />
        <Skeleton className="h-4 w-3/4" />
        <Skeleton className="h-4 w-1/2" />
      </div>
    );
  }

  if (type === 'list') {
    return (
      <div className={`space-y-3 ${className}`}>
        {[1, 2, 3, 4, 5].map((i) => (
          <div key={i} className="flex items-center space-x-4">
            <Skeleton className="w-12 h-12 rounded-full" />
            <div className="space-y-2 flex-1">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-3 w-2/3" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  return <Skeleton className={className} />;
}
