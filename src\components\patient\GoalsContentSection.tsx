
import { GoalCard } from "@/components/patient/GoalCard";
import { GoalsEmptyState } from "@/components/patient/GoalsEmptyState";
import { AIInsightsCard } from "@/components/patient/AIInsightsCard";
import { GoalsStatistics } from "@/components/patient/GoalsStatistics";

interface Goal {
  id: string;
  title: string;
  description: string;
  category: string;
  status: string;
  progress_percentage: number;
  target_date: string;
  created_at: string;
  therapy_type: string;
}

interface GoalsContentSectionProps {
  filteredGoals: Goal[];
  selectedDate: Date;
  goals: Goal[];
  aiInsight: string;
}

export function GoalsContentSection({
  filteredGoals,
  selectedDate,
  goals,
  aiInsight
}: GoalsContentSectionProps) {
  return (
    <div className="space-y-6">
      {/* Goals for Selected Date */}
      <div className="space-y-4">
        {filteredGoals.length === 0 ? (
          <GoalsEmptyState selectedDate={selectedDate} />
        ) : (
          filteredGoals.map((goal) => (
            <GoalCard key={goal.id} goal={goal} />
          ))
        )}
      </div>

      {/* AI Insights Card */}
      <AIInsightsCard insight={aiInsight} />

      {/* Stats Overview */}
      <GoalsStatistics goals={goals} filteredGoals={filteredGoals} />

      {/* Bottom spacing */}
      <div className="h-8"></div>
    </div>
  );
}
