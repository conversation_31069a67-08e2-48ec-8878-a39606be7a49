
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON>rk<PERSON>, Gamepad2 } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { LeezaAIChat } from "@/components/ai/LeezaAIChat";
import { useAuth } from "@/hooks/useAuth";
import DailyEntryForm from "./DailyEntryForm";
import { useNavigate } from "react-router-dom";

export default function ParentDashboard() {
  const { profile } = useAuth();
  const [showLeeza, setShowLeeza] = useState(false);
  const navigate = useNavigate();

  // Fetch today's entries, summary etc. (Stub – further logic can be added)
  // Assume profile includes patient_id
  // const [entry, setEntry] = useState(null);

  return (
    <div className="min-h-screen bg-gray-50 py-6 px-2 sm:px-0">
      <div className="max-w-2xl mx-auto space-y-6">
        {/* Welcome Card */}
        <Card>
          <CardContent className="py-6">
            <h1 className="text-xl font-bold mb-2">
              Hi {profile?.first_name || "Parent"} 👋
            </h1>
            <div className="text-gray-600 mb-2">
              Quick update your child's progress, check off activities, and chat with Leeza AI anytime.
            </div>
          </CardContent>
        </Card>
        {/* Quick Actions */}
        <div className="flex justify-between gap-4">
          <Button
            className="flex-1 bg-app-purple text-white"
            onClick={() => setShowLeeza(true)}
          >
            <Sparkles className="mr-2 h-5 w-5" />
            Ask Leeza AI
          </Button>
          <Button
            className="flex-1 bg-indigo-600 text-white"
            onClick={() => navigate("/patient/game")}
          >
            <Gamepad2 className="mr-2 h-5 w-5" />
            Skills Game
          </Button>
        </div>
        {/* Unified Data Entry */}
        <DailyEntryForm />
        {/* Leeza AI Assistant Modal */}
        <LeezaAIChat isOpen={showLeeza} onClose={() => setShowLeeza(false)} />
      </div>
    </div>
  );
}
