import { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { 
  User, 
  Target, 
  ClipboardList, 
  Calendar, 
  ArrowLeft, 
  Plus,
  TrendingUp,
  Activity,
  Sparkles
} from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { Layout } from "@/components/Layout";
import { Skeleton } from "@/components/ui/skeleton";

interface Patient {
  id: string;
  guardian_name: string;
  autism_level: "1" | "2" | "3";
  date_of_birth: string;
  guardian_email: string;
  guardian_phone: string;
  medical_notes: string;
  emergency_contact: string;
}

interface Goal {
  id: string;
  title: string;
  description: string;
  category: string;
  status: string;
  progress_percentage: number;
  target_date: string;
}

interface Activity {
  id: string;
  title: string;
  description: string;
  category: string;
  scheduled_time: string;
  is_active: boolean;
}

interface Session {
  id: string;
  scheduled_datetime: string;
  duration_minutes: number;
  session_type: string;
  status: string;
  session_notes: string;
}

export default function PatientProfile() {
  const { id: patientId } = useParams();
  const navigate = useNavigate();
  const { profile } = useAuth();
  const { toast } = useToast();
  
  const [patient, setPatient] = useState<Patient | null>(null);
  const [goals, setGoals] = useState<Goal[]>([]);
  const [activities, setActivities] = useState<Activity[]>([]);
  const [sessions, setSessions] = useState<Session[]>([]);
  const [loading, setLoading] = useState(true);
  const [aiInsights, setAiInsights] = useState<string>("");

  useEffect(() => {
    if (patientId && profile?.id) {
      fetchPatientData();
    }
  }, [patientId, profile]);

  const fetchPatientData = async () => {
    try {
      // Fetch patient details
      const { data: patientData, error: patientError } = await supabase
        .from("patients")
        .select("*")
        .eq("id", patientId)
        .eq("assigned_therapist_id", profile?.id)
        .single();

      if (patientError) throw patientError;
      setPatient(patientData);

      // Fetch goals
      const { data: goalsData, error: goalsError } = await supabase
        .from("therapy_goals")
        .select("*")
        .eq("patient_id", patientId)
        .order("created_at", { ascending: false });

      if (goalsError) throw goalsError;
      setGoals(goalsData || []);

      // Fetch activities
      const { data: activitiesData, error: activitiesError } = await supabase
        .from("daily_activities")
        .select("*")
        .eq("patient_id", patientId)
        .order("created_at", { ascending: false });

      if (activitiesError) throw activitiesError;
      setActivities(activitiesData || []);

      // Fetch sessions
      const { data: sessionsData, error: sessionsError } = await supabase
        .from("therapy_sessions")
        .select("*")
        .eq("patient_id", patientId)
        .order("scheduled_datetime", { ascending: false });

      if (sessionsError) throw sessionsError;
      setSessions(sessionsData || []);

      // Generate AI insights
      if (patientData) {
        generateAIInsights(patientData, goalsData || [], activitiesData || [], sessionsData || []);
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const generateAIInsights = async (patientData: Patient, goalsData: Goal[], activitiesData: Activity[], sessionsData: Session[]) => {
    try {
      const response = await supabase.functions.invoke('generate-therapist-insights', {
        body: { 
          patient: patientData,
          goals: goalsData,
          activities: activitiesData,
          sessions: sessionsData,
          type: 'patient_profile'
        }
      });

      if (response.data?.insight) {
        const shortInsight = response.data.insight.split('.')[0] + '.';
        setAiInsights(shortInsight);
      }
    } catch (error) {
      console.log("AI insights unavailable:", error);
    }
  };

  const getAutismLevelLabel = (level: "1" | "2" | "3") => {
    const labels = {
      "1": "Requiring support",
      "2": "Requiring substantial support", 
      "3": "Requiring very substantial support"
    };
    return labels[level];
  };

  const getAge = (dateOfBirth: string) => {
    return new Date().getFullYear() - new Date(dateOfBirth).getFullYear();
  };

  if (loading) {
    return (
      <Layout>
        <div className="min-h-screen bg-gray-50 p-6">
          <div className="max-w-7xl mx-auto space-y-6">
            <Skeleton className="h-8 w-48" />
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[1, 2, 3].map((i) => (
                <Skeleton key={i} className="h-32 rounded-2xl" />
              ))}
            </div>
            <Skeleton className="h-96 rounded-2xl" />
          </div>
        </div>
      </Layout>
    );
  }

  if (!patient) {
    return (
      <Layout>
        <div className="min-h-screen bg-gray-50 p-6">
          <div className="max-w-7xl mx-auto">
            <div className="text-center py-12">
              <User className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Patient Not Found</h3>
              <p className="text-gray-600 mb-4">This patient is not assigned to you or doesn't exist.</p>
              <Button onClick={() => navigate("/therapist/patients")} 
                      className="bg-app-purple hover:bg-app-purple/90 text-white rounded-xl">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Patients
              </Button>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto space-y-8">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button 
                variant="outline" 
                onClick={() => navigate("/therapist/patients")}
                className="border-gray-200 text-gray-600 hover:bg-gray-50 rounded-xl"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Patients
              </Button>
              <div>
                <h1 className="text-4xl font-bold text-gray-900">
                  {patient.guardian_name}'s Profile
                </h1>
                <p className="text-lg text-gray-600">Manage therapy plan and track progress</p>
              </div>
            </div>
          </div>

          {/* AI Insights */}
          {aiInsights && (
            <Card className="bg-app-purple text-white border-0 shadow-sm rounded-2xl">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center space-x-2 text-lg">
                  <Sparkles className="h-5 w-5" />
                  <span>AI Patient Insights</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-white/90 text-sm">{aiInsights}</p>
              </CardContent>
            </Card>
          )}

          {/* Patient Info Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card className="bg-white border-0 shadow-sm rounded-2xl hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">Age</CardTitle>
                <div className="p-2 bg-app-purple/10 rounded-xl">
                  <User className="h-4 w-4 text-app-purple" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900">{getAge(patient.date_of_birth)} years</div>
                <p className="text-xs text-gray-500 mt-1">Current age</p>
              </CardContent>
            </Card>

            <Card className="bg-white border-0 shadow-sm rounded-2xl hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">Autism Level</CardTitle>
                <div className="p-2 bg-app-yellow/10 rounded-xl">
                  <Activity className="h-4 w-4 text-app-yellow" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900">Level {patient.autism_level}</div>
                <p className="text-xs text-gray-500 mt-1">{getAutismLevelLabel(patient.autism_level)}</p>
              </CardContent>
            </Card>

            <Card className="bg-white border-0 shadow-sm rounded-2xl hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">Active Goals</CardTitle>
                <div className="p-2 bg-app-green/10 rounded-xl">
                  <Target className="h-4 w-4 text-app-green" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900">{goals.filter(g => g.status === 'active').length}</div>
                <p className="text-xs text-gray-500 mt-1">In progress</p>
              </CardContent>
            </Card>

            <Card className="bg-white border-0 shadow-sm rounded-2xl hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">Activities</CardTitle>
                <div className="p-2 bg-app-pink/10 rounded-xl">
                  <ClipboardList className="h-4 w-4 text-app-pink" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900">{activities.filter(a => a.is_active).length}</div>
                <p className="text-xs text-gray-500 mt-1">Daily activities</p>
              </CardContent>
            </Card>
          </div>

          {/* Tabs for different sections */}
          <Tabs defaultValue="overview" className="space-y-6">
            <TabsList className="grid w-full grid-cols-4 bg-white border-0 shadow-sm rounded-2xl">
              <TabsTrigger value="overview" className="rounded-xl">Overview</TabsTrigger>
              <TabsTrigger value="goals" className="rounded-xl">Goals ({goals.length})</TabsTrigger>
              <TabsTrigger value="activities" className="rounded-xl">Activities ({activities.length})</TabsTrigger>
              <TabsTrigger value="sessions" className="rounded-xl">Sessions ({sessions.length})</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card className="bg-white border-0 shadow-sm rounded-2xl">
                  <CardHeader>
                    <CardTitle className="text-xl text-gray-900">
                      Patient Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Guardian Name</label>
                      <p className="text-gray-900">{patient.guardian_name}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Guardian Email</label>
                      <p className="text-gray-900">{patient.guardian_email || "Not provided"}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Guardian Phone</label>
                      <p className="text-gray-900">{patient.guardian_phone || "Not provided"}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Emergency Contact</label>
                      <p className="text-gray-900">{patient.emergency_contact || "Not provided"}</p>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-white border-0 shadow-sm rounded-2xl">
                  <CardHeader>
                    <CardTitle className="text-xl text-gray-900">
                      Medical Notes
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-900">{patient.medical_notes || "No medical notes available"}</p>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="goals" className="space-y-6">
              <div className="flex justify-between items-center">
                <h3 className="text-2xl font-bold text-gray-900">Therapy Goals</h3>
                <Button 
                  onClick={() => navigate(`/therapist/goals?patient=${patientId}`)}
                  className="bg-app-green hover:bg-app-green/90 text-white rounded-xl"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Goal
                </Button>
              </div>
              
              {goals.length === 0 ? (
                <Card className="bg-white border-0 shadow-sm rounded-2xl">
                  <CardContent className="text-center py-12">
                    <Target className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">No Goals Set</h3>
                    <p className="text-gray-600">Start by creating therapy goals for this patient.</p>
                  </CardContent>
                </Card>
              ) : (
                <div className="grid gap-4">
                  {goals.map((goal) => (
                    <Card key={goal.id} className="bg-white border-0 shadow-sm rounded-2xl hover:shadow-md transition-shadow">
                      <CardContent className="p-6">
                        <div className="flex justify-between items-start mb-4">
                          <div>
                            <h4 className="font-semibold text-lg text-gray-900">{goal.title}</h4>
                            <p className="text-gray-600">{goal.description}</p>
                          </div>
                          <Badge className="bg-app-green/10 text-app-green border-0">
                            {goal.category}
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4">
                            <Badge 
                              className={goal.status === 'active' 
                                ? 'bg-app-purple/10 text-app-purple border-0' 
                                : 'bg-gray-100 text-gray-600 border-0'}
                            >
                              {goal.status}
                            </Badge>
                            <div className="flex items-center space-x-2">
                              <TrendingUp className="h-4 w-4 text-app-purple" />
                              <span className="text-sm text-gray-600">{goal.progress_percentage}% Complete</span>
                            </div>
                          </div>
                          {goal.target_date && (
                            <div className="text-sm text-gray-500">
                              Target: {new Date(goal.target_date).toLocaleDateString()}
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="activities" className="space-y-6">
              <div className="flex justify-between items-center">
                <h3 className="text-2xl font-bold text-gray-900">Daily Activities</h3>
                <Button 
                  onClick={() => navigate(`/therapist/activities?patient=${patientId}`)}
                  className="bg-app-yellow hover:bg-app-yellow/90 text-white rounded-xl"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Activity
                </Button>
              </div>
              
              {activities.length === 0 ? (
                <Card className="bg-white border-0 shadow-sm rounded-2xl">
                  <CardContent className="text-center py-12">
                    <ClipboardList className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">No Activities Assigned</h3>
                    <p className="text-gray-600">Create daily activities for this patient.</p>
                  </CardContent>
                </Card>
              ) : (
                <div className="grid gap-4">
                  {activities.map((activity) => (
                    <Card key={activity.id} className="bg-white border-0 shadow-sm rounded-2xl hover:shadow-md transition-shadow">
                      <CardContent className="p-6">
                        <div className="flex justify-between items-start mb-4">
                          <div>
                            <h4 className="font-semibold text-lg text-gray-900">{activity.title}</h4>
                            <p className="text-gray-600">{activity.description}</p>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge className="bg-app-yellow/10 text-app-yellow border-0">
                              {activity.category}
                            </Badge>
                            <Badge 
                              className={activity.is_active 
                                ? 'bg-app-green/10 text-app-green border-0' 
                                : 'bg-gray-100 text-gray-600 border-0'}
                            >
                              {activity.is_active ? 'Active' : 'Inactive'}
                            </Badge>
                          </div>
                        </div>
                        {activity.scheduled_time && (
                          <div className="flex items-center space-x-2 text-sm text-gray-500">
                            <Calendar className="h-4 w-4" />
                            <span>Scheduled: {new Date(`2000-01-01T${activity.scheduled_time}`).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</span>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="sessions" className="space-y-6">
              <div className="flex justify-between items-center">
                <h3 className="text-2xl font-bold text-gray-900">Therapy Sessions</h3>
                <Button 
                  onClick={() => navigate(`/therapist/sessions?patient=${patientId}`)}
                  className="bg-app-purple hover:bg-app-purple/90 text-white rounded-xl"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Schedule Session
                </Button>
              </div>
              
              {sessions.length === 0 ? (
                <Card className="bg-white border-0 shadow-sm rounded-2xl">
                  <CardContent className="text-center py-12">
                    <Calendar className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">No Sessions Scheduled</h3>
                    <p className="text-gray-600">Schedule therapy sessions for this patient.</p>
                  </CardContent>
                </Card>
              ) : (
                <div className="grid gap-4">
                  {sessions.map((session) => (
                    <Card key={session.id} className="bg-white border-0 shadow-sm rounded-2xl hover:shadow-md transition-shadow">
                      <CardContent className="p-6">
                        <div className="flex justify-between items-start mb-4">
                          <div>
                            <h4 className="font-semibold text-lg text-gray-900">
                              {new Date(session.scheduled_datetime).toLocaleDateString()} at{' '}
                              {new Date(session.scheduled_datetime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                            </h4>
                            <p className="text-gray-600">{session.duration_minutes} minutes • {session.session_type}</p>
                            {session.session_notes && (
                              <p className="text-gray-600 mt-2">{session.session_notes}</p>
                            )}
                          </div>
                          <Badge 
                            className={
                              session.status === 'completed' 
                                ? 'bg-app-green/10 text-app-green border-0' 
                                : session.status === 'scheduled' 
                                ? 'bg-app-purple/10 text-app-purple border-0' 
                                : 'bg-red-100 text-red-700 border-0'
                            }
                          >
                            {session.status}
                          </Badge>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </Layout>
  );
}
