import { useState } from "react";
import { useAuth } from "@/hooks/useAuth";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { supabase } from "@/integrations/supabase/client";

export default function DailyEntryForm({ isTherapist = false }: { isTherapist?: boolean }) {
  const { profile } = useAuth();
  const [form, setForm] = useState({
    completed_activities: "",
    total_activities: "",
    progress_note: "",
    observation: "",
  });
  const [loading, setLoading] = useState(false);
  const [submitted, setSubmitted] = useState(false);

  const handleChange = (e: any) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e: any) => {
    e.preventDefault();
    setLoading(true);
    setSubmitted(false);
    // Find patient_id, therapist_id as needed—stubbed here (assume one patient per parent for MVP)
    let patient_id = profile?.id;
    let therapist_id = isTherapist ? profile?.id : null;
    let created_by_role = isTherapist ? "therapist" : "parent";

    // COMMENTED OUT: daily_entries submission since table does not exist yet.
    // await supabase.from("daily_entries").insert({
    //   patient_id,
    //   therapist_id,
    //   completed_activities: parseInt(form.completed_activities) || null,
    //   total_activities: parseInt(form.total_activities) || null,
    //   progress_note: form.progress_note,
    //   observation: form.observation,
    //   created_by_role,
    //   entry_date: new Date().toISOString().slice(0, 10),
    // });

    setLoading(false);
    setSubmitted(true);
    setForm({ completed_activities: "", total_activities: "", progress_note: "", observation: "" });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          {isTherapist ? "Today's Patient Progress" : "Report Today's Progress"}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-3">
          <div>
            <label className="block text-gray-700 text-sm mb-1">Completed Activities</label>
            <input type="number" name="completed_activities" min={0} max={10}
              value={form.completed_activities} onChange={handleChange}
              className="border rounded px-2 py-1 w-full" />
          </div>
          <div>
            <label className="block text-gray-700 text-sm mb-1">Total Activities</label>
            <input type="number" name="total_activities" min={0} max={10}
              value={form.total_activities} onChange={handleChange}
              className="border rounded px-2 py-1 w-full" />
          </div>
          <div>
            <label className="block text-gray-700 text-sm mb-1">Note on Progress</label>
            <textarea name="progress_note" value={form.progress_note}
              onChange={handleChange} className="border rounded px-2 py-1 w-full" />
          </div>
          <div>
            <label className="block text-gray-700 text-sm mb-1">Therapy Observation</label>
            <textarea name="observation" value={form.observation}
              onChange={handleChange} className="border rounded px-2 py-1 w-full" />
          </div>
          <Button type="submit" disabled={loading}
            className="bg-app-green text-white w-full">
            {loading ? "Saving..." : "Submit"}
          </Button>
          {submitted && <p className="text-green-600 mt-2">Saved!</p>}
        </form>
      </CardContent>
    </Card>
  );
}
