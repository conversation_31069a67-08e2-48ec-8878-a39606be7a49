
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { systemData } = await req.json();
    const geminiApiKey = Deno.env.get('GEMINI_API_KEY');
    
    if (!geminiApiKey) {
      throw new Error('GEMINI_API_KEY not configured');
    }

    const prompt = `As an AI autism therapy system administrator assistant, analyze this system data and provide administrative insights:

System Overview:
- Total Patients: ${systemData.totalPatients || 0}
- Active Therapists: ${systemData.activeTherapists || 0}
- Sessions This Week: ${systemData.sessionsThisWeek || 0}
- Overall Progress Rate: ${systemData.progressRate || 0}%

Provide strategic insights and recommendations (2-3 sentences) for system administrators to improve overall therapy program effectiveness and patient outcomes.`;

    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${geminiApiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: prompt
          }]
        }]
      }),
    });

    const data = await response.json();
    const insight = data.candidates?.[0]?.content?.parts?.[0]?.text || "Continue monitoring system metrics and consider implementing targeted interventions to improve patient engagement and therapy outcomes.";

    return new Response(JSON.stringify({ insight }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Error:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
