@tailwind base;
@tailwind components;
@tailwind utilities;

/* React Native Web compatibility */
html,
body,
#root {
  height: 100%;
  margin: 0;
  padding: 0;
}

#root {
  display: flex;
  flex-direction: column;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 267 84% 68%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 267 84% 68%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 267 84% 68%;

    --radius: 1rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Enhanced app colors */
    --app-purple: 267 84% 68%;
    --app-purple-light: 267 84% 85%;
    --app-pink: 320 85% 75%;
    --app-yellow: 45 93% 58%;
    --app-green: 142 71% 45%;
    --app-orange: 25 95% 53%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 267 84% 68%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 267 84% 68%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 267 84% 68%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 267 84% 68%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 267 84% 68%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* Enhanced card styles with solid colors */
  .app-card {
    @apply bg-white rounded-xl shadow-sm border-0 transition-all duration-300;
  }

  .app-card-hover {
    @apply hover:shadow-lg hover:scale-[1.02];
  }

  .app-card-interactive {
    @apply cursor-pointer group;
  }

  /* Icon container styles */
  .app-icon-container {
    @apply w-16 h-16 rounded-2xl flex items-center justify-center transition-colors;
  }

  .app-icon-purple {
    @apply bg-app-purple/10 group-hover:bg-app-purple/20;
  }

  .app-icon-yellow {
    @apply bg-app-yellow/10 group-hover:bg-app-yellow/20;
  }

  .app-icon-green {
    @apply bg-app-green/10 group-hover:bg-app-green/20;
  }

  .app-icon-orange {
    @apply bg-app-orange/10 group-hover:bg-app-orange/20;
  }

  /* Enhanced badge styles */
  .app-badge-success {
    @apply bg-emerald-50 text-emerald-700 border-emerald-200;
  }

  .app-badge-warning {
    @apply bg-amber-50 text-amber-700 border-amber-200;
  }

  .app-badge-info {
    @apply bg-blue-50 text-blue-700 border-blue-200;
  }

  .app-badge-neutral {
    @apply bg-gray-50 text-gray-600 border-gray-200;
  }

  /* Animation utilities */
  .app-fade-in {
    @apply animate-in fade-in-0 duration-300;
  }

  .app-slide-up {
    @apply animate-in slide-in-from-bottom-4 duration-300;
  }

  .app-scale-in {
    @apply animate-in zoom-in-95 duration-200;
  }

  /* Focus styles */
  .app-focus {
    @apply focus:ring-2 focus:ring-app-purple focus:ring-offset-2 focus:outline-none;
  }

  /* Loading spinner */
  .app-spinner {
    @apply animate-spin rounded-full border-b-2 border-app-purple;
  }
}

/* Smooth transitions for all interactive elements */
@layer utilities {
  .transition-smooth {
    @apply transition-all duration-300 ease-out;
  }

  .hover-lift {
    @apply hover:scale-[1.02] transition-transform duration-200;
  }

  .hover-glow {
    @apply hover:shadow-lg transition-shadow duration-300;
  }
}
