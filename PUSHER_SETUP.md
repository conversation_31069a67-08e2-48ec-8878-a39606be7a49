
# Pusher Channels Setup Instructions

To complete the Pusher Channels messaging migration, you need to:

## 1. Create a Pusher Account
1. Go to [Pusher Dashboard](https://dashboard.pusher.com/)
2. Sign up for a free account (includes 200k messages/day)
3. Create a new Channels app

## 2. Get Your Pusher Configuration
1. In your Pusher Dashboard, go to your app
2. Click on "App Keys" tab
3. Copy the App ID, Key, Secret, and Cluster

## 3. Update the Configuration
Replace the placeholder values in `src/config/pusher.ts` with your actual Pusher config:

```typescript
const pusherConfig = {
  key: 'your-actual-pusher-key',
  cluster: 'your-actual-cluster', // e.g., 'us2', 'eu', 'ap3'
  encrypted: true,
  forceTLS: true,
};
```

## 4. Set Up Backend Trigger (Required for message delivery)
You'll need to set up a backend service to trigger Pusher events when messages are sent. Options:

### Option A: Supabase Edge Function
Create a Supabase Edge Function that triggers Pusher events when messages are inserted.

### Option B: Webhook Service
Set up a simple webhook service that listens for database changes and triggers Pusher events.

## 5. Enable Supabase Real-time (Alternative)
If you prefer to keep using Supabase real-time:
1. Go to your Supabase Dashboard
2. Navigate to Settings > API
3. Enable real-time for the 'messages' table

## 6. Benefits of Pusher Channels
- Reliable real-time messaging
- Presence channels for user status
- Message history and persistence
- Built-in connection management
- Webhooks for integration
- Great free tier (200k messages/day)

## Note
The app continues to use Supabase for user authentication, contact management, and message storage, while Pusher handles the real-time delivery.
