
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";

interface Goal {
  id: string;
  title: string;
  description: string;
  category: string;
  status: string;
  progress_percentage: number;
  target_date: string;
  created_at: string;
  therapy_type: string;
}

export function useGoalsData() {
  const [goals, setGoals] = useState<Goal[]>([]);
  const [loading, setLoading] = useState(true);
  const [aiInsight, setAiInsight] = useState<string>("");
  const { profile } = useAuth();
  const { toast } = useToast();

  const fetchPatientGoals = async () => {
    try {
      console.log("Starting to fetch goals for user:", profile?.id);
      
      let { data: patientData, error: patientError } = await supabase
        .from("patients")
        .select("id")
        .eq("user_id", profile?.id)
        .maybeSingle();

      console.log("Patient data:", patientData, "Error:", patientError);

      if (patientError) {
        console.error("Error fetching patient:", patientError);
        setGoals([]);
        setLoading(false);
        return;
      }

      if (!patientData) {
        console.log("No patient record found - showing empty state");
        setGoals([]);
        setLoading(false);
        return;
      }

      const { data: goalsData, error: goalsError } = await supabase
        .from("therapy_goals")
        .select("*")
        .eq("patient_id", patientData.id)
        .order("created_at", { ascending: false });

      console.log("Goals data:", goalsData, "Error:", goalsError);

      if (goalsError) {
        console.error("Error fetching goals:", goalsError);
        toast({
          title: "Error",
          description: "Failed to load your goals. Please try again.",
          variant: "destructive",
        });
        setGoals([]);
      } else {
        setGoals(goalsData || []);
        
        if (goalsData && goalsData.length > 0) {
          generateAIInsights(goalsData);
        }
      }
    } catch (error: any) {
      console.error("Unexpected error:", error);
      toast({
        title: "Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
      setGoals([]);
    } finally {
      setLoading(false);
    }
  };

  const generateAIInsights = async (goalsData: Goal[]) => {
    try {
      const response = await supabase.functions.invoke('generate-goal-insights', {
        body: { goals: goalsData }
      });

      if (response.data?.insight) {
        setAiInsight(response.data.insight);
      }
    } catch (error) {
      console.log("AI insights unavailable:", error);
    }
  };

  useEffect(() => {
    if (profile?.id) {
      fetchPatientGoals();
    }
  }, [profile]);

  return {
    goals,
    loading,
    aiInsight,
    fetchPatientGoals
  };
}
