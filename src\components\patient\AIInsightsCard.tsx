
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Sparkles, ChevronDown, ChevronUp } from "lucide-react";
import { useTranslation } from "react-i18next";
import { Collapsible, CollapsibleTrigger, CollapsibleContent } from "@/components/ui/collapsible";
import { useState } from "react";

interface AIInsightsCardProps {
  insight: string;
}

export function AIInsightsCard({ insight }: AIInsightsCardProps) {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);

  if (!insight) return null;

  // Keep insights short and crisp - take only the first sentence
  const shortInsight = insight.split('.')[0] + '.';

  return (
    <Card className="bg-app-purple text-white border-0 shadow-sm rounded-2xl">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center space-x-2 text-lg">
          <Sparkles className="h-5 w-5" />
          <span>{t('patient.aiInsights')}</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Collapsible open={open} onOpenChange={setOpen}>
          <CollapsibleTrigger asChild>
            <button
              className="flex items-center justify-between w-full bg-app-purple/80 hover:bg-app-purple/90 px-4 py-2 rounded-lg focus:outline-none transition-all font-semibold"
              aria-expanded={open}
            >
              <span>{open ? t("Hide Insight") : t("Show Insight")}</span>
              {open ? (
                <ChevronUp className="h-4 w-4 ml-2" />
              ) : (
                <ChevronDown className="h-4 w-4 ml-2" />
              )}
            </button>
          </CollapsibleTrigger>
          <CollapsibleContent className="mt-2">
            <p className="text-white/90 text-sm">{shortInsight}</p>
          </CollapsibleContent>
        </Collapsible>
      </CardContent>
    </Card>
  );
}
