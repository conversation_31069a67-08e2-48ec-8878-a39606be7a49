
import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";
import { format, addDays, subDays, isSameDay, startOfWeek, endOfWeek } from "date-fns";

interface HorizontalDateSliderProps {
  selectedDate: Date;
  onDateChange: (date: Date) => void;
  className?: string;
}

export function HorizontalDateSlider({
  selectedDate,
  onDateChange,
  className,
}: HorizontalDateSliderProps) {
  const [currentWeekStart, setCurrentWeekStart] = useState<Date>(startOfWeek(selectedDate));

  // Generate 7 days from current week start
  const weekDays = Array.from({ length: 7 }, (_, i) => addDays(currentWeekStart, i));

  const goToPreviousWeek = () => {
    setCurrentWeekStart(subDays(currentWeekStart, 7));
  };

  const goToNextWeek = () => {
    setCurrentWeekStart(addDays(currentWeekStart, 7));
  };

  const isToday = (date: Date) => isSameDay(date, new Date());
  const isSelected = (date: Date) => isSameDay(date, selectedDate);

  return (
    <div className={cn("w-full bg-white/80 backdrop-blur-sm rounded-lg p-4 shadow-sm", className)}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-800">
          {format(currentWeekStart, "MMMM yyyy")}
        </h3>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={goToPreviousWeek}
            className="h-8 w-8 p-0"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={goToNextWeek}
            className="h-8 w-8 p-0"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="flex justify-between space-x-2">
        {weekDays.map((date) => (
          <button
            key={date.toISOString()}
            onClick={() => onDateChange(date)}
            className={cn(
              "flex flex-col items-center justify-center w-12 h-16 rounded-lg border-2 transition-all duration-200",
              isSelected(date)
                ? "bg-app-purple text-white border-transparent shadow-lg"
                : isToday(date)
                ? "border-app-purple bg-app-purple/10 text-app-purple"
                : "border-gray-200 bg-white text-gray-700 hover:border-app-purple/50 hover:bg-app-purple/5"
            )}
          >
            <span className="text-xs font-medium">
              {format(date, "EEE")}
            </span>
            <span className={cn(
              "text-lg font-semibold",
              isSelected(date) ? "text-white" : "text-gray-900"
            )}>
              {format(date, "d")}
            </span>
          </button>
        ))}
      </div>
    </div>
  );
}
