
import { ReactNode } from "react";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/hooks/useAuth";
import { LogOut, User, Bell, Globe } from "lucide-react";
import { LanguageSwitcher } from "@/components/LanguageSwitcher";
import { useTranslation } from "react-i18next";

interface LayoutProps {
  children: ReactNode;
}

export function Layout({ children }: LayoutProps) {
  const { profile, signOut } = useAuth();
  const { t } = useTranslation();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Desktop Navigation Header */}
      <header className="bg-white shadow-sm border-b border-gray-100 sticky top-0 z-50 hidden md:block">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              {/* Logo with Leeza AI text */}
              <div className="flex items-center space-x-3">
                <img 
                  src="/lovable-uploads/e4818412-9624-415f-b36e-d55aad3a4e2e.png" 
                  alt="Leeza Logo" 
                  className="h-8 w-auto"
                />
                <div>
                  <h1 className="text-xl font-bold text-gray-900">
                    {t('auth.title')}
                  </h1>
                  <p className="text-xs text-gray-500 font-medium">{t('auth.subtitle')}</p>
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <LanguageSwitcher />

              {/* Notification Button */}
              <Button
                variant="ghost"
                size="sm"
                className="relative p-2 rounded-xl hover:bg-gray-100"
              >
                <Bell className="h-5 w-5 text-gray-600" />
                <span className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full"></span>
              </Button>

              {/* Profile Section */}
              <div className="flex items-center space-x-3 bg-gray-50 rounded-xl px-3 py-2">
                <div className="w-8 h-8 bg-app-purple rounded-full flex items-center justify-center">
                  <User className="h-4 w-4 text-white" />
                </div>
                <div className="text-left">
                  <span className="text-sm font-semibold text-gray-900 block">
                    {profile?.first_name} {profile?.last_name}
                  </span>
                  <span className="text-xs text-app-purple font-medium capitalize">
                    {profile?.role}
                  </span>
                </div>
              </div>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={signOut}
                className="p-2 rounded-xl hover:bg-gray-100"
                title={t('common.logout')}
              >
                <LogOut className="h-4 w-4 text-gray-600" />
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Mobile Navigation Header */}
      <header className="bg-white shadow-sm border-b border-gray-100 sticky top-0 z-50 md:hidden">
        <div className="flex justify-between items-center px-4 py-2 h-14">
          {/* Logo or Icon */}
          <div className="flex items-center space-x-2">
            <img 
              src="/lovable-uploads/e4818412-9624-415f-b36e-d55aad3a4e2e.png" 
              alt="Leeza Logo" 
              className="h-7 w-auto"
            />
            <span className="text-lg font-bold text-gray-900">Leeza</span>
          </div>
          {/* Icons */}
          <div className="flex items-center space-x-3">
            <Button variant="ghost" size="icon" className="p-2 rounded-full relative">
              <Bell className="h-5 w-5 text-gray-600" />
              <span className="absolute -top-1 -right-1 h-2 w-2 bg-red-500 rounded-full"></span>
            </Button>
            <LanguageSwitcher />
            <div className="flex items-center gap-2">
              <div className="w-7 h-7 bg-app-purple rounded-full flex items-center justify-center">
                <User className="h-4 w-4 text-white" />
              </div>
              <span className="text-xs font-semibold text-gray-900 truncate max-w-[80px]">
                {profile?.first_name}
              </span>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={signOut}
              className="p-2 rounded-full"
              title={t('common.logout')}
            >
              <LogOut className="h-4 w-4 text-gray-600" />
            </Button>
          </div>
        </div>
      </header>
      
      <main className="max-w-7xl mx-auto">
        {children}
      </main>
    </div>
  );
}
