
-- Create a trigger function that calls our edge function when a message is inserted
CREATE OR REPLACE FUNCTION trigger_pusher_message() 
RETURNS TRIGGER AS $$
BEGIN
  -- Call the edge function asynchronously
  PERFORM
    net.http_post(
      url := 'https://jttsprfcdcoorslznlcc.supabase.co/functions/v1/trigger-pusher-message',
      headers := jsonb_build_object(
        'Content-Type', 'application/json',
        'Authorization', 'Bearer ' || (SELECT vault.get_secret('SUPABASE_SERVICE_ROLE_KEY'))
      ),
      body := jsonb_build_object(
        'message_id', NEW.id,
        'sender_id', NEW.sender_id,
        'recipient_id', NEW.recipient_id,
        'content', NEW.content,
        'message_type', NEW.message_type,
        'file_url', NEW.file_url,
        'is_read', NEW.is_read,
        'created_at', NEW.created_at
      )
    );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger that fires after each message insert
DROP TRIGGER IF EXISTS on_message_insert ON messages;
CREATE TRIGGER on_message_insert
  AFTER INSERT ON messages
  FOR EACH ROW
  EXECUTE FUNCTION trigger_pusher_message();
