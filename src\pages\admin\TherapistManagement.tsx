
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Search, Plus, User, Mail, Phone, ArrowLeft } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { Layout } from "@/components/Layout";
import { Skeleton } from "@/components/ui/skeleton";
import { useNavigate } from "react-router-dom";
import { TherapistManageModal } from "@/components/admin/TherapistManageModal";
import { useTranslation } from "react-i18next";

interface Therapist {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  created_at: string;
  patient_count?: number;
}

export default function TherapistManagement() {
  const [therapists, setTherapists] = useState<Therapist[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedTherapist, setSelectedTherapist] = useState<Therapist | null>(null);
  const [showManageModal, setShowManageModal] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();
  const { t } = useTranslation();

  useEffect(() => {
    fetchTherapists();
  }, []);

  const fetchTherapists = async () => {
    try {
      const { data, error } = await supabase
        .from("profiles")
        .select("*")
        .eq("role", "therapist")
        .order("created_at", { ascending: false });

      if (error) throw error;

      // Get patient count for each therapist
      const therapistsWithCounts = await Promise.all(
        (data || []).map(async (therapist) => {
          const { count } = await supabase
            .from("patients")
            .select("id", { count: "exact" })
            .eq("assigned_therapist_id", therapist.id);
          
          return {
            ...therapist,
            patient_count: count || 0
          };
        })
      );

      setTherapists(therapistsWithCounts);
    } catch (error: any) {
      toast({
        title: t("common.error"),
        description: t("admin.failedToFetchTherapists"),
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleManage = (therapist: Therapist) => {
    setSelectedTherapist(therapist);
    setShowManageModal(true);
  };

  const handleViewProfile = (therapist: Therapist) => {
    toast({
      title: t("admin.therapistProfile"),
      description: `${therapist.first_name} ${therapist.last_name} - ${t("admin.managingPatients", { count: therapist.patient_count })}`,
    });
  };

  const filteredTherapists = therapists.filter(therapist =>
    therapist.first_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    therapist.last_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    therapist.email?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <Layout>
        <div className="min-h-screen bg-gray-50 p-6">
          <div className="max-w-6xl mx-auto space-y-6">
            <Skeleton className="h-12 w-64" />
            <div className="grid gap-6">
              {[1, 2, 3, 4].map((i) => (
                <Skeleton key={i} className="h-32 rounded-2xl" />
              ))}
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white px-6 py-6 flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate("/dashboard")}
            className="p-2"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-2xl font-bold text-gray-900">{t("admin.therapistManagement")}</h1>
        </div>

        <div className="max-w-6xl mx-auto px-6 space-y-6">
          {/* Search and Actions */}
          <div className="flex items-center justify-between gap-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder={t("admin.searchTherapists")}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 h-12 rounded-xl border-gray-200"
              />
            </div>
            <Button className="bg-app-green hover:bg-app-green/90 text-white rounded-xl h-12 px-6">
              <Plus className="h-4 w-4 mr-2" />
              {t("admin.addTherapist")}
            </Button>
          </div>

          {/* Therapists Grid */}
          <div className="grid gap-6">
            {filteredTherapists.length === 0 ? (
              <Card className="bg-white border-0 shadow-lg rounded-2xl">
                <CardContent className="p-12 text-center">
                  <User className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">{t("admin.noTherapistsFound")}</h3>
                  <p className="text-gray-600">
                    {searchTerm ? t("admin.noTherapistsMatch") : t("admin.noTherapistsRegistered")}
                  </p>
                </CardContent>
              </Card>
            ) : (
              filteredTherapists.map((therapist) => (
                <Card key={therapist.id} className="bg-white border-0 shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300">
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div className="space-y-3">
                        <CardTitle className="text-lg text-gray-900">
                          {therapist.first_name} {therapist.last_name}
                        </CardTitle>
                        <div className="flex items-center space-x-3">
                          <Badge className="bg-app-green text-white">
                            {t("status.active")}
                          </Badge>
                          <Badge variant="outline" className="border-app-purple text-app-purple">
                            {t("admin.patientCount", { count: therapist.patient_count })}
                          </Badge>
                        </div>
                      </div>
                      <div className="text-right">
                        <span className="text-xs text-gray-500">
                          {t("admin.joined")}: {new Date(therapist.created_at).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2 text-sm text-gray-600">
                        <Mail className="h-4 w-4" />
                        <span>{therapist.email}</span>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="text-sm text-gray-600">
                        {therapist.patient_count === 0 ? (
                          <span className="text-orange-600">{t("admin.noPatientsAssigned")}</span>
                        ) : (
                          <span className="text-app-green">{t("admin.managingPatients", { count: therapist.patient_count })}</span>
                        )}
                      </div>
                      <div className="flex space-x-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleViewProfile(therapist)}
                          className="rounded-xl border-app-green text-app-green hover:bg-app-green hover:text-white"
                        >
                          {t("admin.viewProfile")}
                        </Button>
                        <Button 
                          size="sm"
                          onClick={() => handleManage(therapist)}
                          className="bg-app-green hover:bg-app-green/90 text-white rounded-xl"
                        >
                          {t("admin.manage")}
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>

          {/* Bottom spacing */}
          <div className="h-8"></div>
        </div>

        {/* Therapist Management Modal */}
        <TherapistManageModal
          isOpen={showManageModal}
          onClose={() => setShowManageModal(false)}
          therapist={selectedTherapist}
          onUpdate={fetchTherapists}
        />
      </div>
    </Layout>
  );
}
