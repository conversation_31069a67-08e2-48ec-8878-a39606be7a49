
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Target, TrendingUp, Calendar } from "lucide-react";
import { useTranslation } from "react-i18next";

interface Goal {
  id: string;
  title: string;
  description: string;
  category: string;
  status: string;
  progress_percentage: number;
  target_date: string;
  created_at: string;
  therapy_type: string;
}

interface GoalsStatisticsProps {
  goals: Goal[];
  filteredGoals: Goal[];
}

export function GoalsStatistics({ goals, filteredGoals }: GoalsStatisticsProps) {
  const { t } = useTranslation();

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      <Card className="app-card shadow-lg rounded-3xl">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-gray-700">{t('patient.totalGoals')}</CardTitle>
          <Target className="h-4 w-4 text-app-purple" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-app-purple">{goals.length}</div>
        </CardContent>
      </Card>

      <Card className="app-card shadow-lg rounded-3xl">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-gray-700">{t('patient.completed')}</CardTitle>
          <TrendingUp className="h-4 w-4 text-app-green" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-app-green">
            {goals.filter(g => g.status === 'completed').length}
          </div>
        </CardContent>
      </Card>

      <Card className="app-card shadow-lg rounded-3xl">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-gray-700">{t('patient.activeToday')}</CardTitle>
          <Calendar className="h-4 w-4 text-app-yellow" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-app-yellow">
            {filteredGoals.length}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
