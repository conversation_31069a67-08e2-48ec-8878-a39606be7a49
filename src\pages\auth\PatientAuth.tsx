
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { useNavigate } from "react-router-dom";
import { Users } from "lucide-react";
import { useTranslation } from "react-i18next";
import { LanguageSwitcher } from "@/components/LanguageSwitcher";
import { useAuth } from "@/hooks/useAuth";

export default function PatientAuth() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [guardianName, setGuardianName] = useState("");
  const [dateOfBirth, setDateOfBirth] = useState("");
  const [autismLevel, setAutismLevel] = useState<"1" | "2" | "3">("1");
  const [emergencyContact, setEmergencyContact] = useState("");
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { isAuthenticated } = useAuth();

  // Redirect if already authenticated
  useEffect(() => {
    console.log("🔍 PatientAuth: Auth state changed:", { isAuthenticated, loading });
    if (isAuthenticated && !loading) {
      console.log("🚀 PatientAuth: Redirecting to dashboard...");
      navigate("/dashboard");
    }
  }, [isAuthenticated, loading, navigate]);

  const handleGoogleSignIn = async () => {
    setLoading(true);
    try {
      console.log("🔗 Starting Google sign in...");
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/dashboard`,
          queryParams: {
            role: 'patient'
          }
        }
      });

      if (error) {
        console.error("Google sign in error:", error);
        throw error;
      }
      
      console.log("✅ Google sign in initiated");
    } catch (error: any) {
      console.error('Google sign in error:', error);
      toast({
        title: t('common.error'),
        description: error.message || "Failed to sign in with Google",
        variant: "destructive",
      });
      setLoading(false);
    }
  };

  const handleSignUp = async () => {
    if (loading) return;
    
    if (!email || !password || !firstName || !lastName) {
      toast({
        title: t('common.error'),
        description: "Please fill in all required fields (marked with *)",
        variant: "destructive",
      });
      return;
    }
    
    setLoading(true);
    try {
      console.log("🆕 Starting patient signup with data:", {
        email,
        firstName,
        lastName,
        guardianName,
        dateOfBirth,
        autismLevel,
        emergencyContact
      });
      
      const signupData = {
        email,
        password,
        options: {
          data: {
            first_name: firstName,
            last_name: lastName,
            role: 'patient',
            guardian_name: guardianName || `${firstName} ${lastName}`,
            date_of_birth: dateOfBirth || null,
            autism_level: autismLevel,
            emergency_contact: emergencyContact || null,
          },
          emailRedirectTo: `${window.location.origin}/dashboard`
        },
      };
      
      console.log("📤 Sending signup request:", signupData);
      
      const { data, error } = await supabase.auth.signUp(signupData);

      if (error) {
        console.error("❌ Signup error details:", error);
        console.error("❌ Error code:", error.status);
        console.error("❌ Error message:", error.message);
        throw error;
      }

      console.log("✅ Signup successful! Response data:", data);
      console.log("👤 User created:", data.user?.id);
      console.log("🔑 Session created:", !!data.session);
      console.log("📧 Email confirmed:", data.user?.email_confirmed_at);

      // Wait briefly for auth state to update before checking
      setTimeout(() => {
        console.log("🔄 Checking auth state after signup delay...");
      }, 1000);

      // Manually create profile if trigger failed
      if (data.user) {
        console.log("👤 Ensuring profile exists for new user:", data.user.id);
        
        try {
          const { error: profileError } = await supabase
            .from('profiles')
            .insert({
              id: data.user.id,
              email: data.user.email!,
              first_name: firstName,
              last_name: lastName,
              role: 'patient'
            });

          // Only log error if it's not a duplicate key error
          if (profileError && profileError.code !== '23505') {
            console.error("❌ Profile creation error:", profileError);
          } else if (!profileError) {
            console.log("✅ Profile created manually");
          }

          // Create patient record
          const { error: patientError } = await supabase
            .from('patients')
            .insert({
              user_id: data.user.id,
              guardian_name: guardianName || `${firstName} ${lastName}`,
              date_of_birth: dateOfBirth || null,
              autism_level: autismLevel,
              emergency_contact: emergencyContact || null
            });

          if (patientError && patientError.code !== '23505') {
            console.error("❌ Patient creation error:", patientError);
          } else if (!patientError) {
            console.log("✅ Patient record created manually");
          }
        } catch (profileCreationError) {
          console.error("Error in manual profile creation:", profileCreationError);
        }
      }

      if (data.user && !data.user.email_confirmed_at) {
        toast({
          title: t('common.success'),
          description: t('auth.checkEmail'),
        });
      } else if (data.user && data.session) {
        console.log("🎉 User has session, should redirect automatically");
        toast({
          title: t('common.success'),
          description: "Account created successfully!",
        });
      } else {
        toast({
          title: t('common.success'),
          description: "Account created successfully!",
        });
      }
    } catch (error: any) {
      console.error('Signup error:', error);
      let errorMessage = error.message;
      
      if (error.message?.includes('already registered')) {
        errorMessage = "This email is already registered. Please sign in instead.";
      }
      
      toast({
        title: t('common.error'),
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSignIn = async () => {
    if (loading) return;
    
    if (!email || !password) {
      toast({
        title: t('common.error'),
        description: "Please enter both email and password",
        variant: "destructive",
      });
      return;
    }
    
    setLoading(true);
    try {
      console.log("🔑 Starting patient sign in...");
      
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        console.error("Sign in error:", error);
        throw error;
      }

      console.log("✅ Sign in successful:", data);

      toast({
        title: t('auth.welcomeBack'),
        description: t('auth.signedInSuccessfully'),
      });
    } catch (error: any) {
      console.error('Sign in error:', error);
      let errorMessage = error.message;
      
      if (error.message?.includes('Invalid login credentials')) {
        errorMessage = "Invalid email or password. Please check your credentials.";
      }
      
      toast({
        title: t('common.error'),
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 relative overflow-hidden">
      <div className="absolute top-4 right-4 z-20">
        <LanguageSwitcher />
      </div>
      <div className="relative z-10 flex items-center justify-center min-h-screen p-4">
        <div className="w-full max-w-md">
          <div className="text-center mb-8">
            <div className="flex items-center justify-center space-x-3 mb-4">
              <img 
                src="/lovable-uploads/e4818412-9624-415f-b36e-d55aad3a4e2e.png" 
                alt="Leeza Logo" 
                className="h-12 w-auto"
              />
              <div>
                <h1 className="text-3xl font-bold text-gray-900">
                  {t('auth.patientPortal')}
                </h1>
                <p className="text-sm text-gray-500 font-medium">{t('auth.patientDescription')}</p>
              </div>
            </div>
          </div>

          <Card className="bg-white border-0 shadow-xl rounded-2xl overflow-hidden">
            <CardHeader className="text-center pb-4">
              <div className="flex items-center justify-center mb-4">
                <div className="p-4 bg-app-purple rounded-2xl">
                  <Users className="h-8 w-8 text-white" />
                </div>
              </div>
              <CardTitle className="text-2xl font-bold text-app-purple">
                Patient Access
              </CardTitle>
              <CardDescription className="text-gray-600">
                Access your therapy journey
              </CardDescription>
            </CardHeader>
            <CardContent className="px-8 pb-8">
              <div className="mb-6">
                <Button 
                  onClick={handleGoogleSignIn}
                  variant="outline"
                  className="w-full h-12 text-lg font-semibold border-2"
                  disabled={loading}
                >
                  <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
                    <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                  </svg>
                  {loading ? 'Connecting...' : 'Continue with Google'}
                </Button>
              </div>
              
              <div className="relative mb-6">
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t" />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-white px-2 text-gray-500">or</span>
                </div>
              </div>

              <Tabs defaultValue="signin" className="w-full">
                <TabsList className="grid w-full grid-cols-2 mb-6">
                  <TabsTrigger value="signin">Sign In</TabsTrigger>
                  <TabsTrigger value="signup">Sign Up</TabsTrigger>
                </TabsList>
                
                <TabsContent value="signin" className="space-y-4">
                  <Input
                    type="email"
                    placeholder="Email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="h-12"
                    disabled={loading}
                  />
                  <Input
                    type="password"
                    placeholder="Password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="h-12"
                    disabled={loading}
                  />
                  <Button 
                    onClick={handleSignIn} 
                    className="w-full h-12 bg-app-purple hover:bg-app-purple/90"
                    disabled={loading}
                  >
                    {loading ? 'Signing In...' : 'Sign In'}
                  </Button>
                </TabsContent>
                
                <TabsContent value="signup" className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <Input
                      placeholder="First Name*"
                      value={firstName}
                      onChange={(e) => setFirstName(e.target.value)}
                      className="h-12"
                      disabled={loading}
                      required
                    />
                    <Input
                      placeholder="Last Name*"
                      value={lastName}
                      onChange={(e) => setLastName(e.target.value)}
                      className="h-12"
                      disabled={loading}
                      required
                    />
                  </div>
                  <Input
                    type="email"
                    placeholder="Email*"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="h-12"
                    disabled={loading}
                    required
                  />
                  <Input
                    type="password"
                    placeholder="Password*"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="h-12"
                    disabled={loading}
                    required
                  />
                  <Input
                    placeholder="Guardian Name"
                    value={guardianName}
                    onChange={(e) => setGuardianName(e.target.value)}
                    className="h-12"
                    disabled={loading}
                  />
                  <Input
                    type="date"
                    placeholder="Date of Birth"
                    value={dateOfBirth}
                    onChange={(e) => setDateOfBirth(e.target.value)}
                    className="h-12"
                    disabled={loading}
                  />
                  <Select value={autismLevel} onValueChange={(value) => setAutismLevel(value as "1" | "2" | "3")} disabled={loading}>
                    <SelectTrigger className="h-12">
                      <SelectValue placeholder="Autism Level" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">Level 1 - Requiring Support</SelectItem>
                      <SelectItem value="2">Level 2 - Requiring Substantial Support</SelectItem>
                      <SelectItem value="3">Level 3 - Requiring Very Substantial Support</SelectItem>
                    </SelectContent>
                  </Select>
                  <Input
                    placeholder="Emergency Contact"
                    value={emergencyContact}
                    onChange={(e) => setEmergencyContact(e.target.value)}
                    className="h-12"
                    disabled={loading}
                  />
                  <Button 
                    onClick={handleSignUp} 
                    className="w-full h-12 bg-app-purple hover:bg-app-purple/90"
                    disabled={loading}
                  >
                    {loading ? 'Creating Account...' : 'Create Patient Account'}
                  </Button>
                </TabsContent>
              </Tabs>
              
              <div className="mt-6 text-center">
                <p className="text-sm text-gray-600">
                  Are you a therapist? <a href="/auth/therapist" className="text-app-green hover:underline">Sign in here</a>
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
