
import { HorizontalDateSlider } from "@/components/ui/horizontal-date-slider";
import { SessionDetailsCard } from "@/components/ui/session-details-card";
import { ObservationsCard } from "@/components/ui/observations-card";
import { format } from "date-fns";
import { useTranslation } from "react-i18next";

interface TherapySession {
  id: string;
  scheduled_datetime: string;
  duration_minutes: number;
  session_type: string;
  is_virtual: boolean;
  observations: string;
  therapist: {
    first_name: string;
    last_name: string;
  } | null;
}

interface GoalsDateSectionProps {
  selectedDate: Date;
  onDateChange: (date: Date) => void;
  sessionDetails: TherapySession | null;
}

export function GoalsDateSection({
  selectedDate,
  onDateChange,
  sessionDetails
}: GoalsDateSectionProps) {
  const { t } = useTranslation();

  return (
    <div className="space-y-6">
      {/* Date Slider */}
      <HorizontalDateSlider
        selectedDate={selectedDate}
        onDateChange={onDateChange}
      />

      {/* Session Details for Selected Date */}
      <SessionDetailsCard
        title={t('patient.goalsForDate')}
        performedOn={format(selectedDate, "EEEE, MMMM do, yyyy")}
        duration={sessionDetails?.duration_minutes}
        therapyType={sessionDetails?.session_type}
        isVirtual={sessionDetails?.is_virtual}
        status={sessionDetails ? "scheduled" : undefined}
        therapistName={sessionDetails?.therapist ? `${sessionDetails.therapist.first_name} ${sessionDetails.therapist.last_name}` : undefined}
        className="rounded-3xl"
      />

      {/* Session Observations */}
      {sessionDetails && sessionDetails.observations && (
        <ObservationsCard
          observations={[{
            id: sessionDetails.id,
            observation_text: sessionDetails.observations,
            observation_date: format(selectedDate, "yyyy-MM-dd"),
            created_at: sessionDetails.scheduled_datetime,
          }]}
          isTherapist={false}
          className="rounded-3xl"
        />
      )}
    </div>
  );
}
