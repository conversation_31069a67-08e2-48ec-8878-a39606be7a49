
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Users, User<PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import { useTranslation } from "react-i18next";
import { LanguageSwitcher } from "@/components/LanguageSwitcher";
import { useAuth } from "@/hooks/useAuth";

export default function Auth() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { isAuthenticated } = useAuth();

  useEffect(() => {
    if (isAuthenticated) {
      navigate("/dashboard");
    }
  }, [isAuthenticated, navigate]);

  const authRoutes = [
    {
      type: 'patient',
      title: t('auth.patientPortal'),
      description: t('auth.patientDescription'),
      icon: Users,
      color: 'text-app-purple',
      bgColor: 'bg-app-purple',
      route: '/auth/patient'
    },
    {
      type: 'therapist',
      title: t('auth.therapistPortal'), 
      description: t('auth.therapistDescription'),
      icon: User<PERSON><PERSON><PERSON>,
      color: 'text-app-green',
      bgColor: 'bg-app-green',
      route: '/auth/therapist'
    },
    {
      type: 'admin',
      title: t('auth.adminPortal'),
      description: t('auth.adminDescription'),
      icon: Brain,
      color: 'text-app-yellow',
      bgColor: 'bg-app-yellow',
      route: '/auth/admin'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 relative overflow-hidden">
      <div className="absolute top-4 right-4 z-20">
        <LanguageSwitcher />
      </div>
      <div className="relative z-10 flex items-center justify-center min-h-screen p-4">
        <div className="w-full max-w-4xl">
          <div className="text-center mb-8">
            <div className="flex items-center justify-center space-x-3 mb-4">
              <img 
                src="/lovable-uploads/e4818412-9624-415f-b36e-d55aad3a4e2e.png" 
                alt="Leeza Logo" 
                className="h-12 w-auto"
              />
              <div>
                <h1 className="text-4xl font-bold text-gray-900">
                  {t('auth.title')}
                </h1>
                <p className="text-sm text-gray-500 font-medium">{t('auth.subtitle')}</p>
              </div>
            </div>
          </div>

          <Card className="bg-white border-0 shadow-xl rounded-2xl overflow-hidden">
            <CardHeader className="text-center pb-4">
              <CardTitle className="text-3xl font-bold text-gray-900">
                Choose Your Access Type
              </CardTitle>
              <CardDescription className="text-lg text-gray-600">
                Select how you want to access the Leeza platform
              </CardDescription>
            </CardHeader>
            <CardContent className="px-8 pb-8">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {authRoutes.map((route) => {
                  const IconComponent = route.icon;
                  return (
                    <Card 
                      key={route.type}
                      className="cursor-pointer transition-all duration-300 hover:shadow-lg hover:scale-105 border-2 hover:border-gray-300"
                      onClick={() => navigate(route.route)}
                    >
                      <CardContent className="p-6 text-center">
                        <div className={`w-16 h-16 ${route.bgColor} rounded-full flex items-center justify-center mx-auto mb-4`}>
                          <IconComponent className="h-8 w-8 text-white" />
                        </div>
                        <h3 className={`font-bold text-lg mb-2 ${route.color}`}>
                          {route.title}
                        </h3>
                        <p className="text-sm text-gray-600 mb-4">
                          {route.description}
                        </p>
                        <Button 
                          className={`w-full ${route.bgColor} hover:${route.bgColor}/90 text-white`}
                        >
                          Continue as {route.type}
                        </Button>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
