import { Platform } from 'react-native';

// Platform-specific component exports
export * from Platform.select({
  web: () => require('./button'),
  default: () => require('./button.native'),
})();

export * from Platform.select({
  web: () => require('./input'),
  default: () => require('./input.native'),
})();

export * from Platform.select({
  web: () => require('./card'),
  default: () => require('./card.native'),
})();

// Re-export other components that work on both platforms
export * from './toaster';
export * from './sonner';
export * from './tooltip';
