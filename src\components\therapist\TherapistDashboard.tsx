import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Users, Target, ClipboardList, Calendar, TrendingUp, Plus, Sparkles, User, ArrowRight } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { LeezaAIVoiceAgent } from "@/components/ai/LeezaAIVoiceAgent";
import { useVoiceEventHandler } from "@/hooks/useVoiceEventHandler";
import VoiceControl from "@/components/ai/VoiceControl";

interface TherapistStats {
  myPatients: number;
  activeGoals: number;
  sessionsThisWeek: number;
  completionRate: number;
}

interface Patient {
  id: string;
  guardian_name: string;
  autism_level: "1" | "2" | "3";
  date_of_birth: string;
  goals_count?: number;
  activities_count?: number;
  completion_rate?: number;
}

export function TherapistDashboard() {
  const navigate = useNavigate();
  const { profile } = useAuth();
  const { toast } = useToast();
  const [stats, setStats] = useState<TherapistStats | null>(null);
  const [patients, setPatients] = useState<Patient[]>([]);
  const [loading, setLoading] = useState(true);
  const [aiInsights, setAiInsights] = useState<string>("");

  // Enable voice commands for this component
  useVoiceEventHandler();

  useEffect(() => {
    if (profile?.id) {
      fetchTherapistStats();
      fetchMyPatients();
    }
  }, [profile]);

  const fetchTherapistStats = async () => {
    if (!profile?.id) {
      console.log("No profile ID available for therapist stats");
      setLoading(false);
      return;
    }

    try {
      console.log("Fetching therapist stats for:", profile.id);
      const now = new Date();
      const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

      const { data: patients, error: patientsError } = await supabase
        .from("patients")
        .select("id")
        .eq("assigned_therapist_id", profile.id);

      if (patientsError) {
        console.error("Error fetching patients:", patientsError);
        throw patientsError;
      }

      const { data: goals, error: goalsError } = await supabase
        .from("therapy_goals")
        .select("id, progress_percentage")
        .eq("therapist_id", profile.id)
        .eq("status", "active");

      if (goalsError) {
        console.error("Error fetching goals:", goalsError);
        throw goalsError;
      }

      const { data: sessions, error: sessionsError } = await supabase
        .from("therapy_sessions")
        .select("id")
        .eq("therapist_id", profile.id)
        .gte("scheduled_datetime", oneWeekAgo.toISOString());

      if (sessionsError) {
        console.error("Error fetching sessions:", sessionsError);
        throw sessionsError;
      }

      const { data: completions, error: completionsError } = await supabase
        .from("activity_completions")
        .select("id, completed")
        .in("patient_id", (patients || []).map(p => p.id));

      if (completionsError) {
        console.error("Error fetching completions:", completionsError);
        throw completionsError;
      }

      const totalActivities = completions?.length || 0;
      const completedActivities = completions?.filter(c => c.completed).length || 0;
      const completionRate = totalActivities > 0 ? Math.round((completedActivities / totalActivities) * 100) : 0;

      const therapistStats: TherapistStats = {
        myPatients: patients?.length || 0,
        activeGoals: goals?.length || 0,
        sessionsThisWeek: sessions?.length || 0,
        completionRate
      };

      console.log("Therapist stats calculated:", therapistStats);
      setStats(therapistStats);
      
      generateAIInsights(therapistStats, patients || []);
    } catch (error: any) {
      console.error("Error fetching therapist stats:", error);
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchMyPatients = async () => {
    if (!profile?.id) {
      console.log("No profile ID available for patients");
      return;
    }

    try {
      console.log("Fetching patients for therapist:", profile.id);
      const { data: patientsData, error } = await supabase
        .from("patients")
        .select("*")
        .eq("assigned_therapist_id", profile.id)
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching patients:", error);
        throw error;
      }

      const patientsWithStats = await Promise.all(
        (patientsData || []).map(async (patient) => {
          const [goalsData, activitiesData, completionsData] = await Promise.all([
            supabase.from("therapy_goals").select("*", { count: "exact", head: true }).eq("patient_id", patient.id),
            supabase.from("daily_activities").select("*", { count: "exact", head: true }).eq("patient_id", patient.id),
            supabase.from("activity_completions").select("*").eq("patient_id", patient.id).eq("completed", true)
          ]);

          const totalActivities = activitiesData.count || 0;
          const completedActivities = completionsData.data?.length || 0;

          return {
            ...patient,
            goals_count: goalsData.count || 0,
            activities_count: totalActivities,
            completion_rate: totalActivities > 0 ? Math.round((completedActivities / totalActivities) * 100) : 0,
          };
        })
      );

      console.log("Patients with stats:", patientsWithStats);
      setPatients(patientsWithStats);
    } catch (error: any) {
      console.error("Error fetching patients:", error);
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const generateAIInsights = async (statsData: TherapistStats, patients: any[]) => {
    try {
      const response = await supabase.functions.invoke('generate-therapist-insights', {
        body: { 
          patients: patients.map(p => ({ ...p, completion_rate: statsData.completionRate })),
          type: 'dashboard_overview'
        }
      });

      if (response.data?.insight) {
        // Keep AI insights short and crisp
        const shortInsight = response.data.insight.split('.')[0] + '.';
        setAiInsights(shortInsight);
      }
    } catch (error) {
      console.log("AI insights unavailable:", error);
    }
  };

  const getAutismLevelLabel = (level: "1" | "2" | "3") => {
    const labels = {
      "1": "Level 1",
      "2": "Level 2", 
      "3": "Level 3"
    };
    return labels[level];
  };

  const getAge = (dateOfBirth: string) => {
    return new Date().getFullYear() - new Date(dateOfBirth).getFullYear();
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          <Skeleton className="h-16 w-96" />
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[1, 2, 3, 4].map((i) => (
              <Skeleton key={i} className="h-32 rounded-2xl" />
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6 relative">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header Section */}
        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-4 lg:space-y-0">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Therapist Dashboard
            </h1>
            <p className="text-gray-600 text-lg">
              Manage your patients and therapy programs • Voice commands enabled
            </p>
          </div>
          <Button 
            onClick={() => navigate("/therapist/sessions")}
            className="bg-app-purple hover:bg-app-purple/90 text-white px-6 py-3 rounded-2xl shadow-sm font-medium"
          >
            <Plus className="h-4 w-4 mr-2" />
            Schedule Session
          </Button>
        </div>

        {/* Enhanced Voice Agent */}
        <LeezaAIVoiceAgent />

        {/* Voice Control Component */}
        <VoiceControl />

        {/* AI Insights Card */}
        {aiInsights && (
          <Card className="bg-app-purple text-white border-0 shadow-sm rounded-2xl">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center space-x-2 text-lg">
                <Sparkles className="h-5 w-5" />
                <span>AI Insights</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-white/90 text-sm">{aiInsights}</p>
            </CardContent>
          </Card>
        )}

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="bg-white border-0 shadow-sm rounded-2xl hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">My Patients</CardTitle>
              <div className="p-2 bg-app-purple/10 rounded-xl">
                <Users className="h-4 w-4 text-app-purple" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">
                {stats?.myPatients || 0}
              </div>
              <p className="text-xs text-gray-500 mt-1">Active patients</p>
            </CardContent>
          </Card>

          <Card className="bg-white border-0 shadow-sm rounded-2xl hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Active Goals</CardTitle>
              <div className="p-2 bg-app-green/10 rounded-xl">
                <Target className="h-4 w-4 text-app-green" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">
                {stats?.activeGoals || 0}
              </div>
              <p className="text-xs text-gray-500 mt-1">In progress</p>
            </CardContent>
          </Card>

          <Card className="bg-white border-0 shadow-sm rounded-2xl hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">This Week</CardTitle>
              <div className="p-2 bg-app-yellow/10 rounded-xl">
                <Calendar className="h-4 w-4 text-app-yellow" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">
                {stats?.sessionsThisWeek || 0}
              </div>
              <p className="text-xs text-gray-500 mt-1">Sessions scheduled</p>
            </CardContent>
          </Card>

          <Card className="bg-white border-0 shadow-sm rounded-2xl hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Completion Rate</CardTitle>
              <div className="p-2 bg-app-pink/10 rounded-xl">
                <TrendingUp className="h-4 w-4 text-app-pink" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">
                {stats?.completionRate || 0}%
              </div>
              <p className="text-xs text-gray-500 mt-1">Activity completion</p>
            </CardContent>
          </Card>
        </div>

        {/* Patient Profiles Section */}
        <Card className="bg-white border-0 shadow-sm rounded-2xl">
          <CardHeader>
            <CardTitle className="text-xl text-gray-900">
              My Patient Profiles
            </CardTitle>
            <CardDescription className="text-gray-600">
              Click on any patient to manage their therapy plan, goals, activities, and sessions
            </CardDescription>
          </CardHeader>
          <CardContent>
            {patients.length === 0 ? (
              <div className="text-center py-12">
                <Users className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No Patients Assigned</h3>
                <p className="text-gray-500">Contact your administrator to get patients assigned to you.</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {patients.map((patient) => (
                  <Card 
                    key={patient.id} 
                    className="cursor-pointer hover:shadow-md transition-all duration-200 bg-gray-50 border-0 rounded-2xl"
                    onClick={() => navigate(`/therapist/patient/${patient.id}`)}
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-app-purple rounded-full flex items-center justify-center">
                            <User className="h-5 w-5 text-white" />
                          </div>
                          <div>
                            <CardTitle className="text-base font-semibold text-gray-900">
                              {patient.guardian_name}
                            </CardTitle>
                            <p className="text-sm text-gray-500">
                              {getAge(patient.date_of_birth)} years old
                            </p>
                          </div>
                        </div>
                        <ArrowRight className="h-4 w-4 text-gray-400" />
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex items-center justify-between">
                        <Badge className="bg-app-purple/10 text-app-purple border-0">
                          {getAutismLevelLabel(patient.autism_level)}
                        </Badge>
                        <Badge 
                          className={patient.completion_rate && patient.completion_rate > 70 
                            ? "bg-app-green/10 text-app-green border-0" 
                            : "bg-app-yellow/10 text-app-yellow border-0"}
                        >
                          {patient.completion_rate || 0}%
                        </Badge>
                      </div>
                      
                      <div className="grid grid-cols-3 gap-2 text-center">
                        <div>
                          <div className="text-lg font-bold text-app-green">{patient.goals_count || 0}</div>
                          <div className="text-xs text-gray-500">Goals</div>
                        </div>
                        <div>
                          <div className="text-lg font-bold text-app-yellow">{patient.activities_count || 0}</div>
                          <div className="text-xs text-gray-500">Activities</div>
                        </div>
                        <div>
                          <div className="text-lg font-bold text-app-purple">Today</div>
                          <div className="text-xs text-gray-500">Session</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card 
            className="cursor-pointer hover:shadow-md transition-all duration-200 bg-white border-0 rounded-2xl" 
            onClick={() => navigate("/therapist/goals")}
          >
            <CardHeader>
              <CardTitle className="flex items-center space-x-3">
                <div className="p-2 bg-app-green/10 rounded-xl">
                  <Target className="h-5 w-5 text-app-green" />
                </div>
                <span className="text-gray-900">All Goals</span>
              </CardTitle>
              <CardDescription className="text-gray-600">
                View and manage all therapy goals across patients
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button className="w-full bg-app-green hover:bg-app-green/90 text-white rounded-xl font-medium">
                Manage Goals
              </Button>
            </CardContent>
          </Card>

          <Card 
            className="cursor-pointer hover:shadow-md transition-all duration-200 bg-white border-0 rounded-2xl" 
            onClick={() => navigate("/therapist/activities")}
          >
            <CardHeader>
              <CardTitle className="flex items-center space-x-3">
                <div className="p-2 bg-app-yellow/10 rounded-xl">
                  <ClipboardList className="h-5 w-5 text-app-yellow" />
                </div>
                <span className="text-gray-900">All Activities</span>
              </CardTitle>
              <CardDescription className="text-gray-600">
                View and manage all daily activities across patients
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button className="w-full bg-app-yellow hover:bg-app-yellow/90 text-white rounded-xl font-medium">
                Manage Activities
              </Button>
            </CardContent>
          </Card>

          <Card 
            className="cursor-pointer hover:shadow-md transition-all duration-200 bg-white border-0 rounded-2xl" 
            onClick={() => navigate("/therapist/sessions")}
          >
            <CardHeader>
              <CardTitle className="flex items-center space-x-3">
                <div className="p-2 bg-app-purple/10 rounded-xl">
                  <Calendar className="h-5 w-5 text-app-purple" />
                </div>
                <span className="text-gray-900">All Sessions</span>
              </CardTitle>
              <CardDescription className="text-gray-600">
                View and manage all therapy sessions across patients
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button className="w-full bg-app-purple hover:bg-app-purple/90 text-white rounded-xl font-medium">
                Manage Sessions
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

export default TherapistDashboard;
