import React from "react";
import { Platform } from "react-native";
import { NavigationContainer } from "@react-navigation/native";
import { createStackNavigator } from "@react-navigation/stack";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { AuthProvider, useAuth } from "@/hooks/useAuth";
import { useAuthFlow } from "@/hooks/useAuthFlow";
import { ProfileCompletion } from "@/components/auth/ProfileCompletion";
import Auth from "./pages/Auth";
import PatientAuth from "./pages/auth/PatientAuth";
import TherapistAuth from "./pages/auth/TherapistAuth";
import AdminAuth from "./pages/auth/AdminAuth";
import Dashboard from "./pages/Dashboard";
import NotFound from "./pages/NotFound";
import PatientManagement from "./pages/admin/PatientManagement";
import TherapistManagement from "./pages/admin/TherapistManagement";
import SystemReports from "./pages/admin/SystemReports";
import MyPatients from "./pages/therapist/MyPatients";
import PatientProfile from "./pages/therapist/PatientProfile";
import GoalsManagement from "./pages/therapist/GoalsManagement";
import ActivitiesManagement from "./pages/therapist/ActivitiesManagement";
import SessionsManagement from "./pages/therapist/SessionsManagement";
import Goals from "./pages/patient/Goals";
import Activities from "./pages/patient/Activities";
import Milestones from "./pages/patient/Milestones";
import Sessions from "./pages/patient/Sessions";
import SkillsGame from "./pages/patient/SkillsGame";
import ParentDashboard from "@/components/dashboards/ParentDashboard";
import TherapistDashboard from "@/components/dashboards/TherapistDashboard";
import AdminDashboard from "@/components/dashboards/AdminDashboard";

// Platform-specific imports
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";

const Stack = createStackNavigator();

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000,
      gcTime: 10 * 60 * 1000,
      retry: 1,
    },
  },
});

function LoadingScreen() {
  if (Platform.OS === "web") {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-app-purple mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  } else {
    // For React Native, we'll use a simple loading component
    return React.createElement(
      "div",
      {
        style: {
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100vh",
        },
      },
      "Loading..."
    );
  }
}

function ProtectedRoute({ children }: { children: React.ReactNode }) {
  const { isAuthenticated, loading: authLoading, profile } = useAuth();
  const { authFlow, loading: flowLoading } = useAuthFlow();

  if (authLoading || flowLoading) {
    return <LoadingScreen />;
  }

  if (!isAuthenticated) {
    return <Navigate to="/auth" replace />;
  }

  // Check if profile completion is needed
  if (authFlow && !authFlow.profile_completed && profile?.role !== "admin") {
    return (
      <ProfileCompletion userType={profile?.role as "patient" | "therapist"} />
    );
  }

  return <>{children}</>;
}

function AppContent() {
  const { isAuthenticated, loading } = useAuth();
  if (loading) return <LoadingScreen />;

  return (
    <>
      <Routes>
        <Route path="/" element={<Navigate to="/dashboard" replace />} />
        <Route path="/auth" element={<Auth />} />
        <Route path="/auth/patient" element={<PatientAuth />} />
        <Route path="/auth/therapist" element={<TherapistAuth />} />
        <Route path="/auth/admin" element={<AdminAuth />} />
        <Route
          path="/dashboard"
          element={
            <ProtectedRoute>
              <Dashboard />
            </ProtectedRoute>
          }
        />

        {/* Restore all detailed application and role-specific patient/therapist/admin routes */}
        <Route
          path="/patient/goals"
          element={
            <ProtectedRoute>
              <Goals />
            </ProtectedRoute>
          }
        />
        <Route
          path="/patient/activities"
          element={
            <ProtectedRoute>
              <Activities />
            </ProtectedRoute>
          }
        />
        <Route
          path="/patient/milestones"
          element={
            <ProtectedRoute>
              <Milestones />
            </ProtectedRoute>
          }
        />
        <Route
          path="/patient/sessions"
          element={
            <ProtectedRoute>
              <Sessions />
            </ProtectedRoute>
          }
        />
        <Route
          path="/patient/game"
          element={
            <ProtectedRoute>
              <SkillsGame />
            </ProtectedRoute>
          }
        />
        <Route
          path="/admin/patients"
          element={
            <ProtectedRoute>
              <PatientManagement />
            </ProtectedRoute>
          }
        />
        <Route
          path="/admin/therapists"
          element={
            <ProtectedRoute>
              <TherapistManagement />
            </ProtectedRoute>
          }
        />
        <Route
          path="/admin/reports"
          element={
            <ProtectedRoute>
              <SystemReports />
            </ProtectedRoute>
          }
        />
        <Route
          path="/therapist/goals"
          element={
            <ProtectedRoute>
              <GoalsManagement />
            </ProtectedRoute>
          }
        />
        <Route
          path="/therapist/activities"
          element={
            <ProtectedRoute>
              <ActivitiesManagement />
            </ProtectedRoute>
          }
        />
        <Route
          path="/therapist/sessions"
          element={
            <ProtectedRoute>
              <SessionsManagement />
            </ProtectedRoute>
          }
        />
        <Route
          path="/therapist/patients"
          element={
            <ProtectedRoute>
              <MyPatients />
            </ProtectedRoute>
          }
        />
        <Route
          path="/therapist/patient/:id"
          element={
            <ProtectedRoute>
              <PatientProfile />
            </ProtectedRoute>
          }
        />
        <Route path="*" element={<NotFound />} />
      </Routes>
    </>
  );
}

function AppNavigator() {
  const { isAuthenticated, loading: authLoading, profile } = useAuth();
  const { authFlow, loading: flowLoading } = useAuthFlow();

  if (authLoading || flowLoading) {
    return <LoadingScreen />;
  }

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}
    >
      {!isAuthenticated ? (
        <>
          <Stack.Screen name="Auth" component={Auth} />
          <Stack.Screen name="PatientAuth" component={PatientAuth} />
          <Stack.Screen name="TherapistAuth" component={TherapistAuth} />
          <Stack.Screen name="AdminAuth" component={AdminAuth} />
        </>
      ) : !profile ? (
        <Stack.Screen name="ProfileCompletion" component={ProfileCompletion} />
      ) : (
        <>
          <Stack.Screen name="Dashboard" component={Dashboard} />
          <Stack.Screen
            name="PatientManagement"
            component={PatientManagement}
          />
          <Stack.Screen
            name="TherapistManagement"
            component={TherapistManagement}
          />
          <Stack.Screen name="SystemReports" component={SystemReports} />
          <Stack.Screen name="MyPatients" component={MyPatients} />
          <Stack.Screen name="PatientProfile" component={PatientProfile} />
          <Stack.Screen name="GoalsManagement" component={GoalsManagement} />
          <Stack.Screen
            name="ActivitiesManagement"
            component={ActivitiesManagement}
          />
          <Stack.Screen
            name="SessionsManagement"
            component={SessionsManagement}
          />
          <Stack.Screen name="Goals" component={Goals} />
          <Stack.Screen name="Activities" component={Activities} />
          <Stack.Screen name="Milestones" component={Milestones} />
          <Stack.Screen name="Sessions" component={Sessions} />
          <Stack.Screen name="SkillsGame" component={SkillsGame} />
          <Stack.Screen name="NotFound" component={NotFound} />
        </>
      )}
    </Stack.Navigator>
  );
}

const App = () => {
  // For now, we'll focus on web support
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <AuthProvider>
            <div className="min-h-screen bg-gray-50">
              <AppContent />
            </div>
          </AuthProvider>
        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
  );
};

export default App;
