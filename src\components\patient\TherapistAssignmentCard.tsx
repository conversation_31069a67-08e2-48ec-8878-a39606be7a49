
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { User, Stethoscope, Calendar, Clock } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/hooks/useAuth";
import { format } from "date-fns";
import { useTranslation } from "react-i18next";

interface TherapistAssignmentCardProps {
  selectedDate: Date;
}

interface TherapistInfo {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  role: string;
}

interface SessionInfo {
  id: string;
  session_type: string;
  scheduled_datetime: string;
  duration_minutes: number;
  is_virtual: boolean;
  status: string;
}

export function TherapistAssignmentCard({ selectedDate }: TherapistAssignmentCardProps) {
  const [therapistInfo, setTherapistInfo] = useState<TherapistInfo | null>(null);
  const [sessionInfo, setSessionInfo] = useState<SessionInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const { profile } = useAuth();
  const { t } = useTranslation();

  useEffect(() => {
    const fetchTherapistAssignment = async () => {
      if (!profile?.id) {
        console.log("❌ No profile ID available");
        return;
      }
      
      setLoading(true);
      console.log("🔍 Starting therapist assignment fetch for user:", profile.id);
      
      try {
        // Get patient data with assigned therapist ID
        console.log("📋 Fetching patient data...");
        const { data: patientData, error: patientError } = await supabase
          .from("patients")
          .select("id, assigned_therapist_id")
          .eq("user_id", profile.id)
          .maybeSingle();

        console.log("👤 Patient query result:", { patientData, patientError });

        if (patientError) {
          console.error("❌ Error fetching patient data:", patientError);
          setTherapistInfo(null);
          setSessionInfo(null);
          return;
        }

        if (!patientData) {
          console.log("⚠️ No patient record found for user:", profile.id);
          setTherapistInfo(null);
          setSessionInfo(null);
          return;
        }

        console.log("✅ Patient found:", patientData);

        // Check if therapist is assigned
        if (!patientData.assigned_therapist_id) {
          console.log("⚠️ No therapist assigned to patient");
          setTherapistInfo(null);
          setSessionInfo(null);
          return;
        }

        console.log("🩺 Fetching therapist info for ID:", patientData.assigned_therapist_id);
        
        // Fetch therapist info using maybeSingle() to prevent errors when not found
        const { data: therapistData, error: therapistError } = await supabase
          .from("profiles")
          .select("id, first_name, last_name, email, role")
          .eq("id", patientData.assigned_therapist_id)
          .eq("role", "therapist")
          .maybeSingle();

        console.log("🩺 Therapist query result:", { therapistData, therapistError });

        if (therapistError) {
          console.error("❌ Error fetching therapist:", therapistError);
          setTherapistInfo(null);
        } else if (!therapistData) {
          console.log("⚠️ No therapist found with ID and role=therapist:", patientData.assigned_therapist_id);
          setTherapistInfo(null);
        } else {
          console.log("✅ Therapist found:", therapistData);
          setTherapistInfo(therapistData);
        }

        // Fetch session for selected date if we have valid patient data
        console.log("📅 Fetching session for date:", selectedDate);
        const startOfDay = new Date(selectedDate);
        startOfDay.setHours(0, 0, 0, 0);
        
        const endOfDay = new Date(selectedDate);
        endOfDay.setHours(23, 59, 59, 999);

        const { data: sessionData, error: sessionError } = await supabase
          .from("therapy_sessions")
          .select("id, session_type, scheduled_datetime, duration_minutes, is_virtual, status")
          .eq("patient_id", patientData.id)
          .eq("therapist_id", patientData.assigned_therapist_id)
          .gte("scheduled_datetime", startOfDay.toISOString())
          .lte("scheduled_datetime", endOfDay.toISOString())
          .maybeSingle();

        console.log("📅 Session query result:", { sessionData, sessionError });

        if (sessionError) {
          console.error("❌ Error fetching session:", sessionError);
          setSessionInfo(null);
        } else {
          console.log("📅 Session data:", sessionData);
          setSessionInfo(sessionData);
        }
      } catch (error) {
        console.error("💥 Unexpected error in fetchTherapistAssignment:", error);
        setTherapistInfo(null);
        setSessionInfo(null);
      } finally {
        setLoading(false);
        console.log("🏁 Therapist assignment fetch completed");
      }
    };

    fetchTherapistAssignment();
  }, [selectedDate, profile?.id]);

  if (loading) {
    return (
      <Card className="rounded-3xl bg-gradient-to-r from-purple-50 to-blue-50 border-0">
        <CardContent className="p-4">
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!therapistInfo) {
    console.log("🔄 Rendering no therapist state");
    return (
      <Card className="rounded-3xl bg-gradient-to-r from-gray-50 to-gray-100 border-0">
        <CardContent className="p-4">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center">
              <User className="h-6 w-6 text-gray-500" />
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-700">{t('patient.noTherapistAssigned')}</p>
              <p className="text-xs text-gray-500">{t('patient.contactCareCoordinator')}</p>
            </div>
            <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
              {t('patient.pending')}
            </Badge>
          </div>
        </CardContent>
      </Card>
    );
  }

  console.log("🔄 Rendering therapist assigned state for:", therapistInfo);

  const getSessionTypeDisplay = (sessionType: string) => {
    return t(`therapyTypes.${sessionType}`, sessionType);
  };

  const getStatusColor = (status: string) => {
    const colors = {
      "scheduled": "bg-blue-100 text-blue-800",
      "confirmed": "bg-green-100 text-green-800", 
      "completed": "bg-gray-100 text-gray-800",
      "cancelled": "bg-red-100 text-red-800",
      "rescheduled": "bg-orange-100 text-orange-800"
    };
    return colors[status as keyof typeof colors] || "bg-gray-100 text-gray-800";
  };

  return (
    <Card className="rounded-3xl bg-gradient-to-r from-purple-50 to-blue-50 border-0 shadow-lg">
      <CardHeader className="pb-3">
        <CardTitle className="text-sm font-medium text-gray-600">{t('patient.assignedTherapist')}</CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-4">
          {/* Therapist Info */}
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-app-purple rounded-full flex items-center justify-center">
              <Stethoscope className="h-6 w-6 text-white" />
            </div>
            <div className="flex-1">
              <p className="text-base font-semibold text-gray-900">
                {therapistInfo.first_name} {therapistInfo.last_name}
              </p>
              <p className="text-sm text-gray-600">{therapistInfo.email}</p>
            </div>
            <Badge className="bg-green-100 text-green-800 border-green-200">
              {t('patient.assigned')}
            </Badge>
          </div>

          {/* Session Info for Selected Date */}
          {sessionInfo ? (
            <div className="bg-white/60 rounded-2xl p-3 space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4 text-app-purple" />
                  <span className="text-sm font-medium text-gray-900">
                    {format(selectedDate, 'MMM d, yyyy')}
                  </span>
                </div>
                <Badge className={getStatusColor(sessionInfo.status)}>
                  {t(`status.${sessionInfo.status}`, sessionInfo.status)}
                </Badge>
              </div>
              
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <div className="flex items-center space-x-1">
                  <Clock className="h-3 w-3" />
                  <span>{format(new Date(sessionInfo.scheduled_datetime), 'h:mm a')}</span>
                </div>
                <div>
                  <span>{sessionInfo.duration_minutes} {t('patient.minutes')}</span>
                </div>
                <div>
                  <span>{sessionInfo.is_virtual ? t('patient.virtual') : t('patient.inPerson')}</span>
                </div>
              </div>

              <div className="pt-1">
                <Badge variant="outline" className="bg-app-purple/10 text-app-purple border-app-purple/20">
                  {getSessionTypeDisplay(sessionInfo.session_type)}
                </Badge>
              </div>
            </div>
          ) : (
            <div className="bg-white/40 rounded-2xl p-3">
              <div className="flex items-center space-x-2 text-gray-600">
                <Calendar className="h-4 w-4" />
                <span className="text-sm">
                  {t('patient.noSessionScheduled', { date: format(selectedDate, 'MMM d') })}
                </span>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
