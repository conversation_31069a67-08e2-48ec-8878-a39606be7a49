
import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Users, Calendar, Target, TrendingUp, Sparkles, User, ArrowRight } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { Layout } from "@/components/Layout";
import { Skeleton } from "@/components/ui/skeleton";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";

interface Patient {
  id: string;
  guardian_name: string;
  autism_level: "1" | "2" | "3";
  date_of_birth: string;
  created_at: string;
  goals_count?: number;
  activities_count?: number;
  completion_rate?: number;
}

export default function MyPatients() {
  const [patients, setPatients] = useState<Patient[]>([]);
  const [loading, setLoading] = useState(true);
  const [aiInsights, setAiInsights] = useState<string>("");
  const { profile } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const { t } = useTranslation();

  useEffect(() => {
    if (profile?.id) {
      fetchMyPatients();
    }
  }, [profile]);

  const fetchMyPatients = async () => {
    try {
      const { data: patientsData, error } = await supabase
        .from("patients")
        .select("*")
        .eq("assigned_therapist_id", profile?.id);

      if (error) throw error;

      // Fetch additional data for each patient
      const patientsWithStats = await Promise.all(
        (patientsData || []).map(async (patient) => {
          const [goalsData, activitiesData, completionsData] = await Promise.all([
            supabase.from("therapy_goals").select("*", { count: "exact", head: true }).eq("patient_id", patient.id),
            supabase.from("daily_activities").select("*", { count: "exact", head: true }).eq("patient_id", patient.id),
            supabase.from("activity_completions").select("*").eq("patient_id", patient.id).eq("completed", true)
          ]);

          const totalActivities = activitiesData.count || 0;
          const completedActivities = completionsData.data?.length || 0;

          return {
            ...patient,
            goals_count: goalsData.count || 0,
            activities_count: totalActivities,
            completion_rate: totalActivities > 0 ? Math.round((completedActivities / totalActivities) * 100) : 0,
          };
        })
      );

      setPatients(patientsWithStats);
      
      // Generate AI insights
      if (patientsWithStats.length > 0) {
        generateAIInsights(patientsWithStats);
      }
    } catch (error: any) {
      toast({
        title: t("common.error"),
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const generateAIInsights = async (patientsData: Patient[]) => {
    try {
      const response = await supabase.functions.invoke('generate-therapist-insights', {
        body: { 
          patients: patientsData,
          type: 'patient_overview'
        }
      });

      if (response.data?.insight) {
        const shortInsight = response.data.insight.split('.')[0] + '.';
        setAiInsights(shortInsight);
      }
    } catch (error) {
      console.log("AI insights unavailable:", error);
    }
  };

  const getAutismLevelLabel = (level: "1" | "2" | "3") => {
    return t(`autismLevels.${level}`);
  };

  if (loading) {
    return (
      <Layout>
        <div className="min-h-screen bg-gray-50 p-6">
          <div className="max-w-7xl mx-auto space-y-6">
            <Skeleton className="h-8 w-48" />
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[1, 2, 3].map((i) => (
                <Skeleton key={i} className="h-32 rounded-2xl" />
              ))}
            </div>
            <Skeleton className="h-96 rounded-2xl" />
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto space-y-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-2">
              {t("therapist.patients")}
            </h1>
            <p className="text-lg text-gray-600">{t("therapist.manageAndTrackPatients")}</p>
          </div>

          {/* AI Insights Card */}
          {aiInsights && (
            <Card className="bg-app-purple text-white border-0 shadow-sm rounded-2xl">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center space-x-2 text-lg">
                  <Sparkles className="h-5 w-5" />
                  <span>{t("patient.aiInsights")}</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-white/90 text-sm">{aiInsights}</p>
              </CardContent>
            </Card>
          )}

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="bg-white border-0 shadow-sm rounded-2xl hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">{t("therapist.totalPatients")}</CardTitle>
                <div className="p-2 bg-app-purple/10 rounded-xl">
                  <Users className="h-4 w-4 text-app-purple" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900">{patients.length}</div>
                <p className="text-xs text-gray-500 mt-1">{t("therapist.assignedToYou")}</p>
              </CardContent>
            </Card>

            <Card className="bg-white border-0 shadow-sm rounded-2xl hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">{t("therapist.activeGoals")}</CardTitle>
                <div className="p-2 bg-app-green/10 rounded-xl">
                  <Target className="h-4 w-4 text-app-green" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900">
                  {patients.reduce((sum, p) => sum + (p.goals_count || 0), 0)}
                </div>
                <p className="text-xs text-gray-500 mt-1">{t("therapist.totalGoals")}</p>
              </CardContent>
            </Card>

            <Card className="bg-white border-0 shadow-sm rounded-2xl hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">{t("therapist.avgCompletion")}</CardTitle>
                <div className="p-2 bg-app-yellow/10 rounded-xl">
                  <TrendingUp className="h-4 w-4 text-app-yellow" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900">
                  {patients.length > 0 
                    ? Math.round(patients.reduce((sum, p) => sum + (p.completion_rate || 0), 0) / patients.length)
                    : 0}%
                </div>
                <p className="text-xs text-gray-500 mt-1">{t("therapist.activityCompletion")}</p>
              </CardContent>
            </Card>
          </div>

          {/* Patients Cards */}
          <Card className="bg-white border-0 shadow-sm rounded-2xl">
            <CardHeader>
              <CardTitle className="text-xl text-gray-900">
                {t("therapist.patientOverview")}
              </CardTitle>
              <CardDescription className="text-gray-600">
                {t("therapist.clickToManage")}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {patients.length === 0 ? (
                <div className="text-center py-12">
                  <Users className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">{t("therapist.noPatientsAssigned")}</h3>
                  <p className="text-gray-600">{t("therapist.contactAdministrator")}</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {patients.map((patient) => {
                    const age = new Date().getFullYear() - new Date(patient.date_of_birth).getFullYear();
                    return (
                      <Card 
                        key={patient.id} 
                        className="cursor-pointer hover:shadow-md transition-all duration-200 bg-gray-50 border-0 rounded-2xl"
                        onClick={() => navigate(`/therapist/patient/${patient.id}`)}
                      >
                        <CardHeader className="pb-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <div className="w-10 h-10 bg-app-purple rounded-full flex items-center justify-center">
                                <User className="h-5 w-5 text-white" />
                              </div>
                              <div>
                                <CardTitle className="text-base font-semibold text-gray-900">
                                  {patient.guardian_name}
                                </CardTitle>
                                <p className="text-sm text-gray-500">
                                  {t("therapist.yearsOld", { age })}
                                </p>
                              </div>
                            </div>
                            <ArrowRight className="h-4 w-4 text-gray-400" />
                          </div>
                        </CardHeader>
                        <CardContent className="space-y-3">
                          <div className="flex items-center justify-between">
                            <Badge className="bg-app-purple/10 text-app-purple border-0">
                              {getAutismLevelLabel(patient.autism_level)}
                            </Badge>
                            <Badge 
                              className={patient.completion_rate && patient.completion_rate > 70 
                                ? "bg-app-green/10 text-app-green border-0" 
                                : "bg-app-yellow/10 text-app-yellow border-0"}
                            >
                              {patient.completion_rate || 0}%
                            </Badge>
                          </div>
                          
                          <div className="grid grid-cols-3 gap-2 text-center">
                            <div>
                              <div className="text-lg font-bold text-app-green">{patient.goals_count || 0}</div>
                              <div className="text-xs text-gray-500">{t("patient.goals")}</div>
                            </div>
                            <div>
                              <div className="text-lg font-bold text-app-yellow">{patient.activities_count || 0}</div>
                              <div className="text-xs text-gray-500">{t("patient.activities")}</div>
                            </div>
                            <div>
                              <div className="text-lg font-bold text-app-purple">
                                {patient.completion_rate || 0}%
                              </div>
                              <div className="text-xs text-gray-500">{t("therapist.complete")}</div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </Layout>
  );
}
