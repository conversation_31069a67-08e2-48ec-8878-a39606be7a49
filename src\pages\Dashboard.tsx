
import { useAuth } from "@/hooks/useAuth";
import { AdminDashboard } from "@/components/admin/AdminDashboard";
import { TherapistDashboard } from "@/components/therapist/TherapistDashboard";
import { PatientDashboard } from "@/components/patient/PatientDashboard";
import { Layout } from "@/components/Layout";
import { EnhancedLoading } from "@/components/ui/enhanced-loading";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertTriangle, RefreshCw, User } from "lucide-react";
import { Button } from "@/components/ui/button";

export default function Dashboard() {
  const { profile, loading, user, isAuthenticated } = useAuth();

  // Show loading state only during initial auth check or when fetching profile for new users
  if (loading) {
    return <EnhancedLoading type="dashboard" />;
  }

  // If user is authenticated but no profile exists, show profile creation prompt
  if (isAuthenticated && !profile) {
    return (
      <Layout>
        <div className="p-6 max-w-2xl mx-auto">
          <Alert className="border-app-purple">
            <User className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold text-lg mb-2">Welcome! Profile Setup Required</h3>
                  <p className="text-gray-600 mb-3">
                    Your account has been created successfully, but we need to set up your profile to continue.
                  </p>
                </div>
                
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm text-gray-700 mb-2"><strong>Account Details:</strong></p>
                  <p className="text-sm text-gray-600">Email: {user?.email}</p>
                  <p className="text-sm text-gray-600">User ID: {user?.id}</p>
                </div>

                <div className="flex space-x-3">
                  <Button 
                    onClick={() => window.location.reload()} 
                    variant="outline" 
                    size="sm"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Refresh
                  </Button>
                  <Button 
                    onClick={() => window.location.href = '/auth'} 
                    variant="default" 
                    size="sm"
                    className="bg-app-purple hover:bg-app-purple/90"
                  >
                    Complete Setup
                  </Button>
                </div>
              </div>
            </AlertDescription>
          </Alert>
        </div>
      </Layout>
    );
  }

  // Render dashboard based on the profile's role
  if (!profile) {
    return (
      <Layout>
        <div className="p-6">
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Unable to load your profile. Please try refreshing the page or contact support.
            </AlertDescription>
          </Alert>
        </div>
      </Layout>
    );
  }

  switch (profile.role) {
    case "patient":
      return (
        <Layout>
          <PatientDashboard />
        </Layout>
      );
    case "therapist":
      return (
        <Layout>
          <TherapistDashboard />
        </Layout>
      );
    case "admin":
      return (
        <Layout>
          <AdminDashboard />
        </Layout>
      );
    default:
      return (
        <Layout>
          <div className="p-6">
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Your account role ({profile.role || 'unknown'}) is not recognized. Please contact support.
              </AlertDescription>
            </Alert>
          </div>
        </Layout>
      );
  }
}
