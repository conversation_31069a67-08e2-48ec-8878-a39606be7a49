
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Calendar } from "lucide-react";
import { useTranslation } from "react-i18next";

interface Goal {
  id: string;
  title: string;
  description: string;
  category: string;
  status: string;
  progress_percentage: number;
  target_date: string;
  created_at: string;
  therapy_type: string;
}

interface GoalCardProps {
  goal: Goal;
}

export function GoalCard({ goal }: GoalCardProps) {
  const { t } = useTranslation();

  const getCategoryColor = (category: string) => {
    const colors = {
      communication: "bg-app-purple text-white",
      social: "bg-app-green text-white",
      behavior: "bg-app-yellow text-white",
      cognitive: "bg-app-pink text-white",
      motor: "bg-app-orange text-white"
    };
    return colors[category as keyof typeof colors] || "bg-gray-500 text-white";
  };

  const getTherapyTypeColor = (therapyType: string) => {
    const colors = {
      "aba_therapy": "bg-blue-500 text-white",
      "speech_therapy": "bg-pink-500 text-white",
      "occupational_therapy": "bg-orange-500 text-white",
      "behavioral_therapy": "bg-teal-500 text-white"
    };
    return colors[therapyType as keyof typeof colors] || "bg-gray-500 text-white";
  };

  return (
    <Card className="app-card app-card-hover shadow-lg rounded-3xl">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div className="space-y-3">
            <CardTitle className="text-lg text-gray-900">{goal.title}</CardTitle>
            <div className="flex items-center space-x-2 flex-wrap gap-2">
              <Badge className={getCategoryColor(goal.category)}>
                {t(`categories.${goal.category}`)}
              </Badge>
              {goal.therapy_type && (
                <Badge className={getTherapyTypeColor(goal.therapy_type)}>
                  {t(`therapyTypes.${goal.therapy_type}`)}
                </Badge>
              )}
              <Badge 
                className={`${
                  goal.status === 'completed' ? 'bg-green-500 text-white' :
                  goal.status === 'active' ? 'bg-app-purple text-white' :
                  goal.status === 'paused' ? 'bg-yellow-500 text-white' :
                  'bg-gray-500 text-white'
                }`}
              >
                {t(`status.${goal.status}`)}
              </Badge>
            </div>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-app-purple">
              {goal.progress_percentage}%
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-gray-600">{goal.description}</p>
        
        <div className="space-y-2">
          <div className="flex justify-between text-sm text-gray-600">
            <span>{t('patient.progress')}</span>
            <span>{goal.progress_percentage}%</span>
          </div>
          <Progress value={goal.progress_percentage} className="h-2" />
        </div>

        {goal.target_date && (
          <div className="flex items-center text-gray-600">
            <Calendar className="h-4 w-4 mr-2" />
            <span className="text-sm">{t('patient.target')}: {new Date(goal.target_date).toLocaleDateString()}</span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
