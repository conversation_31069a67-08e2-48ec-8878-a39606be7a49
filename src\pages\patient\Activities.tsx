import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, Clock, Calendar, Sparkles, Activity, User, ArrowLeft } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { Layout } from "@/components/Layout";
import { Skeleton } from "@/components/ui/skeleton";
import { HorizontalDateSlider } from "@/components/ui/horizontal-date-slider";
import { SessionDetailsCard } from "@/components/ui/session-details-card";
import { format, isSameDay, isAfter, isBefore, parseISO } from "date-fns";
import { useNavigate } from "react-router-dom";

interface DailyActivity {
  id: string;
  title: string;
  description: string;
  category: string;
  scheduled_time: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  start_date?: string;
  end_date?: string;
  completions?: ActivityCompletion[];
}

interface ActivityCompletion {
  id: string;
  completed: boolean;
  completion_date: string;
  notes: string;
}

interface TherapySession {
  id: string;
  scheduled_datetime: string;
  duration_minutes: number;
  session_type: string;
  is_virtual: boolean;
  therapist: {
    first_name: string;
    last_name: string;
  };
}

export default function Activities() {
  const [activities, setActivities] = useState<DailyActivity[]>([]);
  const [loading, setLoading] = useState(true);
  const [aiRecommendations, setAiRecommendations] = useState<string>("");
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [filteredActivities, setFilteredActivities] = useState<DailyActivity[]>([]);
  const [sessionDetails, setSessionDetails] = useState<TherapySession | null>(null);
  const { profile } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  useEffect(() => {
    if (profile?.id) {
      fetchPatientActivities();
    }
  }, [profile]);

  useEffect(() => {
    const filtered = activities.filter(activity => {
      if (!activity.start_date && !activity.end_date) {
        return true;
      }
      
      const startDate = activity.start_date ? parseISO(activity.start_date) : parseISO(activity.created_at);
      const endDate = activity.end_date ? parseISO(activity.end_date) : new Date();
      
      return (isAfter(selectedDate, startDate) || isSameDay(selectedDate, startDate)) &&
             (isBefore(selectedDate, endDate) || isSameDay(selectedDate, endDate));
    });
    setFilteredActivities(filtered);
    
    fetchSessionForDate(selectedDate);
  }, [activities, selectedDate]);

  const fetchPatientActivities = async () => {
    try {
      console.log("Starting to fetch activities for user:", profile?.id);
      
      let { data: patientData, error: patientError } = await supabase
        .from("patients")
        .select("id")
        .eq("user_id", profile?.id)
        .maybeSingle();

      console.log("Patient data:", patientData, "Error:", patientError);

      if (patientError) {
        console.error("Error fetching patient:", patientError);
        setActivities([]);
        setLoading(false);
        return;
      }

      if (!patientData) {
        console.log("No patient record found - showing empty state");
        setActivities([]);
        setLoading(false);
        return;
      }

      const { data: activitiesData, error: activitiesError } = await supabase
        .from("daily_activities")
        .select(`
          *,
          completions:activity_completions(*)
        `)
        .eq("patient_id", patientData.id)
        .eq("is_active", true)
        .order("scheduled_time", { ascending: true });

      console.log("Activities data:", activitiesData, "Error:", activitiesError);

      if (activitiesError) {
        console.error("Error fetching activities:", activitiesError);
        toast({
          title: "Error",
          description: "Failed to load your activities. Please try again.",
          variant: "destructive",
        });
        setActivities([]);
      } else {
        setActivities(activitiesData || []);
        
        if (activitiesData && activitiesData.length > 0) {
          generateAIRecommendations(activitiesData);
        }
      }
    } catch (error: any) {
      console.error("Unexpected error:", error);
      toast({
        title: "Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
      setActivities([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchSessionForDate = async (date: Date) => {
    try {
      let { data: patientData, error: patientError } = await supabase
        .from("patients")
        .select("id")
        .eq("user_id", profile?.id)
        .maybeSingle();

      if (patientError || !patientData) return;

      const startOfDay = new Date(date);
      startOfDay.setHours(0, 0, 0, 0);
      
      const endOfDay = new Date(date);
      endOfDay.setHours(23, 59, 59, 999);

      const { data: sessionData, error: sessionError } = await supabase
        .from("therapy_sessions")
        .select(`
          *,
          therapist:profiles!therapist_id(first_name, last_name)
        `)
        .eq("patient_id", patientData.id)
        .gte("scheduled_datetime", startOfDay.toISOString())
        .lte("scheduled_datetime", endOfDay.toISOString())
        .maybeSingle();

      if (!sessionError && sessionData) {
        setSessionDetails(sessionData);
      } else {
        setSessionDetails(null);
      }
    } catch (error) {
      console.error("Error fetching session for date:", error);
      setSessionDetails(null);
    }
  };

  const generateAIRecommendations = async (activitiesData: DailyActivity[]) => {
    try {
      const response = await supabase.functions.invoke('generate-activity-recommendations', {
        body: { activities: activitiesData }
      });

      if (response.data?.recommendations) {
        setAiRecommendations(response.data.recommendations);
      }
    } catch (error) {
      console.log("AI recommendations unavailable:", error);
    }
  };

  const markActivityComplete = async (activityId: string) => {
    try {
      const { data: patientData } = await supabase
        .from("patients")
        .select("id")
        .eq("user_id", profile?.id)
        .single();

      if (!patientData) return;

      const { error } = await supabase
        .from("activity_completions")
        .insert({
          activity_id: activityId,
          patient_id: patientData.id,
          completed: true,
          completion_date: selectedDate.toISOString().split('T')[0],
          notes: "Completed by patient"
        });

      if (error) throw error;

      toast({
        title: "Activity Completed!",
        description: "Great job completing your activity!",
      });

      fetchPatientActivities();
    } catch (error: any) {
      console.error("Error marking activity complete:", error);
      toast({
        title: "Error",
        description: "Failed to mark activity as complete. Please try again.",
        variant: "destructive",
      });
    }
  };

  const getCategoryColor = (category: string) => {
    const colors = {
      communication: "bg-app-purple text-white",
      social: "bg-app-green text-white",
      behavior: "bg-app-yellow text-white",
      cognitive: "bg-app-pink text-white",
      motor: "bg-app-orange text-white"
    };
    return colors[category as keyof typeof colors] || "bg-gray-500 text-white";
  };

  const isActivityCompletedForDate = (activity: DailyActivity) => {
    const dateString = selectedDate.toISOString().split('T')[0];
    return activity.completions?.some(
      completion => completion.completion_date === dateString && completion.completed
    );
  };

  if (loading) {
    return (
      <Layout>
        <div className="min-h-screen bg-gray-50 p-4">
          <div className="max-w-md mx-auto space-y-6">
            <Skeleton className="h-20 w-full rounded-2xl" />
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <Skeleton key={i} className="h-32 w-full rounded-2xl" />
              ))}
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white px-6 py-6 flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate(-1)}
            className="p-2"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-xl font-bold text-gray-900">Daily Activities</h1>
        </div>

        <div className="max-w-md mx-auto px-6 space-y-6">
          {/* Date Slider */}
          <HorizontalDateSlider
            selectedDate={selectedDate}
            onDateChange={setSelectedDate}
          />

          {/* Session Details for Selected Date */}
          <SessionDetailsCard
            title="Activities for Selected Date"
            performedOn={format(selectedDate, "EEEE, MMMM do, yyyy")}
            duration={sessionDetails?.duration_minutes}
            therapyType={sessionDetails?.session_type}
            isVirtual={sessionDetails?.is_virtual}
            status={sessionDetails ? "scheduled" : undefined}
            className="rounded-3xl"
          />

          {/* Therapist Info */}
          {sessionDetails && (
            <Card className="app-card shadow-lg rounded-3xl">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <User className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-medium text-gray-900">
                    Therapist: {sessionDetails.therapist?.first_name} {sessionDetails.therapist?.last_name}
                  </span>
                </div>
              </CardContent>
            </Card>
          )}

          {/* AI Recommendations Card */}
          {aiRecommendations && (
            <Card className="app-gradient-purple text-white border-0 shadow-lg rounded-3xl">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Sparkles className="h-6 w-6" />
                  <span>AI Activity Recommendations</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-white/90">{aiRecommendations}</p>
              </CardContent>
            </Card>
          )}

          {/* Activities List */}
          <div className="space-y-4">
            {filteredActivities.length === 0 ? (
              <Card className="app-card shadow-lg rounded-3xl">
                <CardContent className="p-12 text-center">
                  <Activity className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">No Activities for This Date</h3>
                  <p className="text-gray-600">No activities are assigned for {format(selectedDate, "MMMM do, yyyy")}.</p>
                </CardContent>
              </Card>
            ) : (
              filteredActivities.map((activity) => {
                const isCompletedForDate = isActivityCompletedForDate(activity);
                return (
                  <Card key={activity.id} className="app-card app-card-hover shadow-lg rounded-3xl">
                    <CardHeader>
                      <div className="flex justify-between items-start">
                        <div className="space-y-3">
                          <CardTitle className="text-lg text-gray-900">{activity.title}</CardTitle>
                          <div className="flex items-center space-x-2 flex-wrap gap-2">
                            <Badge className={getCategoryColor(activity.category)}>
                              {activity.category}
                            </Badge>
                            {isCompletedForDate && (
                              <Badge className="bg-green-500 text-white">
                                Completed for {format(selectedDate, "MMM d")}
                              </Badge>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Clock className="h-4 w-4 text-gray-500" />
                          <span className="text-sm text-gray-600">
                            {activity.scheduled_time ? 
                              new Date(`2000-01-01T${activity.scheduled_time}`).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) : 
                              'Anytime'
                            }
                          </span>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <p className="text-gray-600">{activity.description}</p>
                      
                      <div className="flex items-center justify-between flex-wrap gap-4">
                        <div className="text-sm text-gray-500">
                          {activity.start_date && activity.end_date ? (
                            <span>
                              Active: {format(parseISO(activity.start_date), "MMM d")} - {format(parseISO(activity.end_date), "MMM d, yyyy")}
                            </span>
                          ) : (
                            <span>Daily activity</span>
                          )}
                        </div>
                        <Button 
                          size="sm"
                          onClick={() => markActivityComplete(activity.id)}
                          disabled={isCompletedForDate}
                          className="app-gradient-purple text-white border-0 rounded-full disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <CheckCircle className="h-4 w-4 mr-2" />
                          {isCompletedForDate ? "Completed" : "Mark Complete"}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                );
              })
            )}
          </div>

          {/* Bottom spacing */}
          <div className="h-8"></div>
        </div>
      </div>
    </Layout>
  );
}
