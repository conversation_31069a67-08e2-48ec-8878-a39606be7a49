
-- First, drop ALL existing policies on profiles to start clean
DROP POLICY IF EXISTS "<PERSON><PERSON> can view all profiles" ON public.profiles;
DROP POLICY IF EXISTS "Users can view own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;

-- Drop the existing function and recreate it with better isolation
DROP FUNCTION IF EXISTS public.get_current_user_role();

-- Create a more robust security definer function that avoids recursion
CREATE OR REPLACE FUNCTION public.get_user_role_safe()
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
STABLE
AS $$
DECLARE
  user_role TEXT;
BEGIN
  -- Direct query to avoid RLS recursion
  SELECT role INTO user_role 
  FROM public.profiles 
  WHERE id = auth.uid()
  LIMIT 1;
  
  RETURN COALESCE(user_role, 'patient');
END;
$$;

-- Create simple, non-recursive policies
CREATE POLICY "Enable read access for own profile" 
ON public.profiles 
FOR SELECT 
USING (auth.uid() = id);

CREATE POLICY "Enable update access for own profile" 
ON public.profiles 
FOR UPDATE 
USING (auth.uid() = id);

-- Create admin policy using the safe function
CREATE POLICY "Enable admin read access" 
ON public.profiles 
FOR SELECT 
USING (public.get_user_role_safe() = 'admin');

-- Ensure RLS is enabled
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
