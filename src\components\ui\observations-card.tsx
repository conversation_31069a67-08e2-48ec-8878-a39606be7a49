import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { MessageSquare, Plus, Save } from "lucide-react";
import { cn } from "@/lib/utils";

interface Observation {
  id: string;
  observation_text: string;
  category?: string;
  observation_date: string;
  created_at: string;
}

interface ObservationsCardProps {
  observations?: Observation[];
  onAddObservation?: (text: string, category?: string) => void;
  isTherapist?: boolean;
  className?: string;
}

export function ObservationsCard({
  observations = [],
  onAddObservation,
  isTherapist = false,
  className,
}: ObservationsCardProps) {
  const [newObservation, setNewObservation] = useState("");
  const [isAdding, setIsAdding] = useState(false);

  const handleSaveObservation = () => {
    if (newObservation.trim() && onAddObservation) {
      onAddObservation(newObservation.trim());
      setNewObservation("");
      setIsAdding(false);
    }
  };

  return (
    <Card className={cn("bg-white/80 backdrop-blur-sm border-0 shadow-lg", className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <MessageSquare className="h-5 w-5 text-purple-500" />
            <span>Session Observations</span>
          </CardTitle>
          {isTherapist && !isAdding && (
            <Button
              size="sm"
              onClick={() => setIsAdding(true)}
              className="bg-gradient-to-r from-purple-500 to-yellow-500 hover:from-purple-600 hover:to-yellow-600 text-white"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Observation
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Add new observation form */}
        {isAdding && (
          <div className="space-y-3 p-4 bg-purple-50 rounded-lg border-2 border-purple-200">
            <Textarea
              placeholder="Enter your observation about this session..."
              value={newObservation}
              onChange={(e) => setNewObservation(e.target.value)}
              className="min-h-[100px] resize-none"
            />
            <div className="flex items-center space-x-2">
              <Button
                size="sm"
                onClick={handleSaveObservation}
                disabled={!newObservation.trim()}
                className="bg-gradient-to-r from-purple-500 to-yellow-500 hover:from-purple-600 hover:to-yellow-600 text-white"
              >
                <Save className="h-4 w-4 mr-2" />
                Save
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  setIsAdding(false);
                  setNewObservation("");
                }}
              >
                Cancel
              </Button>
            </div>
          </div>
        )}

        {/* Existing observations */}
        {observations.length === 0 ? (
          <div className="text-center text-gray-500 py-6">
            <MessageSquare className="h-12 w-12 text-gray-300 mx-auto mb-2" />
            <p>No observations recorded for this session</p>
          </div>
        ) : (
          <div className="space-y-3">
            {observations.map((observation) => (
              <div
                key={observation.id}
                className="p-4 bg-gray-50 rounded-lg border border-gray-200"
              >
                <div className="flex items-start justify-between mb-2">
                  {observation.category && (
                    <Badge variant="secondary" className="mb-2">
                      {observation.category}
                    </Badge>
                  )}
                  <span className="text-xs text-gray-500">
                    {new Date(observation.observation_date).toLocaleDateString()}
                  </span>
                </div>
                <p className="text-gray-700 text-sm leading-relaxed">
                  {observation.observation_text}
                </p>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
