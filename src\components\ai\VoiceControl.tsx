
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Mic, MicOff, Volume2, VolumeX, Sparkles } from 'lucide-react';
import { useVoiceAgent } from '@/hooks/useVoiceAgent';

const VoiceControl = () => {
  const {
    isListening,
    isProcessing,
    isSpeaking,
    lastCommand,
    lastParsedCommand,
    startListening,
    stopListening,
    stopSpeaking
  } = useVoiceAgent();

  return (
    <Card className="fixed bottom-6 right-6 bg-white/95 backdrop-blur-xl shadow-xl border-app-purple/20 max-w-sm">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center space-x-2 text-app-purple">
          <Sparkles className="h-5 w-5" />
          <span><PERSON>za Voice Assistant</span>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="flex items-center justify-center space-x-3">
          <Button
            onClick={isListening ? stopListening : startListening}
            disabled={isProcessing}
            className={`rounded-full p-4 ${
              isListening 
                ? "bg-red-500 hover:bg-red-600 animate-pulse" 
                : "bg-app-purple hover:bg-app-purple/90"
            }`}
          >
            {isListening ? <MicOff className="h-6 w-6" /> : <Mic className="h-6 w-6" />}
          </Button>

          {isSpeaking && (
            <Button
              onClick={stopSpeaking}
              variant="outline"
              className="rounded-full p-4"
            >
              <VolumeX className="h-6 w-6" />
            </Button>
          )}
        </div>

        <div className="text-center text-sm">
          {isListening && <span className="text-red-600 font-medium">🎤 Listening...</span>}
          {isProcessing && <span className="text-blue-600 font-medium">🔄 Processing...</span>}
          {isSpeaking && <span className="text-green-600 font-medium">🔊 Speaking...</span>}
          {!isListening && !isProcessing && !isSpeaking && (
            <span className="text-gray-500">Ready for voice commands</span>
          )}
        </div>

        {lastCommand && (
          <div className="p-3 bg-gray-50 rounded-lg">
            <div className="text-xs font-medium text-gray-700 mb-1">Last Command:</div>
            <div className="text-sm text-gray-900">{lastCommand}</div>
          </div>
        )}

        {lastParsedCommand && (
          <div className="p-3 bg-app-purple/10 rounded-lg">
            <div className="text-xs font-medium text-app-purple mb-1">Action:</div>
            <div className="text-sm text-gray-900">{lastParsedCommand.action}</div>
            {lastParsedCommand.response && (
              <div className="text-xs text-gray-600 mt-1">{lastParsedCommand.response}</div>
            )}
            {lastParsedCommand.confidence && (
              <div className={`text-xs mt-1 px-2 py-1 rounded ${
                lastParsedCommand.confidence === 'high' ? 'bg-green-100 text-green-800' :
                lastParsedCommand.confidence === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'
              }`}>
                Confidence: {lastParsedCommand.confidence}
              </div>
            )}
          </div>
        )}

        <div className="text-xs text-gray-600 space-y-1">
          <div className="font-medium">Try saying:</div>
          <div>• "Show all my patients"</div>
          <div>• "Create goal for Arjun"</div>
          <div>• "Schedule session tomorrow"</div>
          <div>• "Update progress to 75%"</div>
          <div>• "Generate report for patient"</div>
        </div>
      </CardContent>
    </Card>
  );
};

export default VoiceControl;
