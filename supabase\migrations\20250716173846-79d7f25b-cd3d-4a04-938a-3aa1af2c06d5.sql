-- Update handle_new_user function to handle Google OAuth role assignment
CREATE OR REPLACE FUNCTION public.handle_new_user()
R<PERSON><PERSON><PERSON> trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  user_role TEXT;
BEGIN
  -- Temporarily disable <PERSON><PERSON> for this session to allow profile creation
  PERFORM set_config('row_security', 'off', true);
  
  -- Determine user role - check metadata first, then default to patient
  user_role := COALESCE(NEW.raw_user_meta_data ->> 'role', 'patient');
  
  -- Insert into profiles table
  INSERT INTO public.profiles (id, email, first_name, last_name, role, google_id)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data ->> 'first_name', ''),
    COALESCE(NEW.raw_user_meta_data ->> 'last_name', ''),
    user_role,
    COALESCE(NEW.raw_user_meta_data ->> 'provider_id', NEW.raw_user_meta_data ->> 'sub')
  );

  -- Insert into auth_flows table 
  INSERT INTO public.auth_flows (user_id, flow_type, google_auth, profile_completed)
  VALUES (
    NEW.id,
    user_role,
    CASE 
      WHEN NEW.raw_user_meta_data ->> 'provider' = 'google' THEN TRUE
      ELSE FALSE 
    END,
    FALSE
  );

  -- If the user is a patient, create a patient record
  IF user_role = 'patient' THEN
    INSERT INTO public.patients (
      user_id,
      guardian_name,
      date_of_birth,
      autism_level,
      emergency_contact
    )
    VALUES (
      NEW.id,
      COALESCE(
        NEW.raw_user_meta_data ->> 'guardian_name',
        TRIM(CONCAT(
          COALESCE(NEW.raw_user_meta_data ->> 'first_name', ''), 
          ' ', 
          COALESCE(NEW.raw_user_meta_data ->> 'last_name', '')
        ))
      ),
      CASE 
        WHEN NEW.raw_user_meta_data ->> 'date_of_birth' IS NOT NULL 
        THEN (NEW.raw_user_meta_data ->> 'date_of_birth')::date 
        ELSE NULL 
      END,
      COALESCE((NEW.raw_user_meta_data ->> 'autism_level')::autism_level, '1'::autism_level),
      NEW.raw_user_meta_data ->> 'emergency_contact'
    );
  END IF;

  -- Re-enable RLS
  PERFORM set_config('row_security', 'on', true);

  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Ensure RLS is re-enabled even on error
    PERFORM set_config('row_security', 'on', true);
    RAISE;
END;
$$;