
import React, { useState, useEffect } from "react";
import { Calendar } from "@/components/ui/calendar";
import { Slider } from "@/components/ui/slider";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import { CalendarIcon, Clock, ChevronLeft, ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";
import { format } from "date-fns";

interface DateTimeSliderProps {
  value?: Date;
  onChange: (date: Date) => void;
  placeholder?: string;
  className?: string;
  showTime?: boolean;
  minDate?: Date;
  maxDate?: Date;
  disabled?: boolean;
}

export function DateTimeSlider({
  value,
  onChange,
  placeholder = "Select date and time",
  className,
  showTime = true,
  minDate,
  maxDate,
  disabled = false,
}: DateTimeSliderProps) {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(value);
  const [hours, setHours] = useState<number[]>([value ? value.getHours() : 9]);
  const [minutes, setMinutes] = useState<number[]>([value ? value.getMinutes() : 0]);
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    if (value) {
      setSelectedDate(value);
      setHours([value.getHours()]);
      setMinutes([value.getMinutes()]);
    }
  }, [value]);

  const handleDateSelect = (date: Date | undefined) => {
    if (date) {
      setSelectedDate(date);
      updateDateTime(date, hours[0], minutes[0]);
    }
  };

  const handleHoursChange = (newHours: number[]) => {
    setHours(newHours);
    if (selectedDate) {
      updateDateTime(selectedDate, newHours[0], minutes[0]);
    }
  };

  const handleMinutesChange = (newMinutes: number[]) => {
    setMinutes(newMinutes);
    if (selectedDate) {
      updateDateTime(selectedDate, hours[0], newMinutes[0]);
    }
  };

  const updateDateTime = (date: Date, hour: number, minute: number) => {
    const newDateTime = new Date(date);
    newDateTime.setHours(hour, minute, 0, 0);
    onChange(newDateTime);
  };

  const quickTimePresets = [
    { label: "9:00 AM", hours: 9, minutes: 0 },
    { label: "10:30 AM", hours: 10, minutes: 30 },
    { label: "2:00 PM", hours: 14, minutes: 0 },
    { label: "3:30 PM", hours: 15, minutes: 30 },
    { label: "5:00 PM", hours: 17, minutes: 0 },
  ];

  const handleQuickTime = (preset: { hours: number; minutes: number }) => {
    setHours([preset.hours]);
    setMinutes([preset.minutes]);
    if (selectedDate) {
      updateDateTime(selectedDate, preset.hours, preset.minutes);
    }
  };

  const formatDisplayValue = () => {
    if (!value) return placeholder;
    if (showTime) {
      return format(value, "PPP 'at' h:mm a");
    }
    return format(value, "PPP");
  };

  return (
    <div className={cn("w-full", className)}>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              "w-full justify-start text-left font-normal",
              !value && "text-muted-foreground"
            )}
            disabled={disabled}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {formatDisplayValue()}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <div className="flex">
            {/* Calendar Section */}
            <div className="p-3 border-r">
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={handleDateSelect}
                disabled={(date) => {
                  if (minDate && date < minDate) return true;
                  if (maxDate && date > maxDate) return true;
                  return false;
                }}
                initialFocus
                className="pointer-events-auto"
              />
            </div>

            {/* Time Section */}
            {showTime && (
              <div className="p-4 space-y-6 min-w-[280px]">
                <div className="space-y-2">
                  <h4 className="font-medium flex items-center">
                    <Clock className="mr-2 h-4 w-4" />
                    Time Selection
                  </h4>
                  
                  {/* Quick Time Presets */}
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">Quick Times</p>
                    <div className="flex flex-wrap gap-1">
                      {quickTimePresets.map((preset) => (
                        <Badge
                          key={preset.label}
                          variant="secondary"
                          className="cursor-pointer hover:bg-app-purple hover:text-white transition-colors"
                          onClick={() => handleQuickTime(preset)}
                        >
                          {preset.label}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Hour Slider */}
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <label className="text-sm font-medium">Hours</label>
                      <span className="text-lg font-mono bg-muted px-2 py-1 rounded">
                        {hours[0].toString().padStart(2, '0')}
                      </span>
                    </div>
                    <Slider
                      value={hours}
                      onValueChange={handleHoursChange}
                      max={23}
                      min={0}
                      step={1}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>12 AM</span>
                      <span>12 PM</span>
                      <span>11 PM</span>
                    </div>
                  </div>

                  {/* Minutes Slider */}
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <label className="text-sm font-medium">Minutes</label>
                      <span className="text-lg font-mono bg-muted px-2 py-1 rounded">
                        {minutes[0].toString().padStart(2, '0')}
                      </span>
                    </div>
                    <Slider
                      value={minutes}
                      onValueChange={handleMinutesChange}
                      max={59}
                      min={0}
                      step={5}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>:00</span>
                      <span>:30</span>
                      <span>:59</span>
                    </div>
                  </div>

                  {/* Selected Time Display */}
                  {selectedDate && (
                    <Card className="bg-app-purple/10 border-app-purple/20">
                      <CardContent className="p-3">
                        <p className="text-center font-medium text-app-purple">
                          {format(
                            new Date(selectedDate.getFullYear(), selectedDate.getMonth(), selectedDate.getDate(), hours[0], minutes[0]),
                            "EEEE, MMMM do, yyyy 'at' h:mm a"
                          )}
                        </p>
                      </CardContent>
                    </Card>
                  )}
                </div>

                <div className="flex gap-2 pt-2 border-t">
                  <Button
                    variant="outline"
                    className="flex-1"
                    onClick={() => setIsOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    className="flex-1 bg-app-purple hover:bg-app-purple/90 text-white"
                    onClick={() => setIsOpen(false)}
                    disabled={!selectedDate}
                  >
                    Confirm
                  </Button>
                </div>
              </div>
            )}
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
